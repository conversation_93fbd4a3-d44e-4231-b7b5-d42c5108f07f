package com.ecommerce.flink;

import com.ecommerce.flink.algorithm.RecommendationEngine;
import com.ecommerce.flink.model.Product;
import com.ecommerce.flink.model.Recommendation;
import com.ecommerce.flink.model.UserBehavior;
import com.ecommerce.flink.serialization.JsonDeserializationSchema;
import com.ecommerce.flink.serialization.JsonSerializationSchema;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;

/**
 * Flink推荐系统作业
 */
public class RecommendationJob {
    
    private static final Logger logger = LoggerFactory.getLogger(RecommendationJob.class);
    
    private static final String KAFKA_BOOTSTRAP_SERVERS = "localhost:9092,localhost:9093,localhost:9094";
    private static final String USER_BEHAVIOR_TOPIC = "user-behavior";
    private static final String PRODUCT_EVENTS_TOPIC = "product-events";
    private static final String RECOMMENDATIONS_TOPIC = "recommendations";
    
    public static void main(String[] args) throws Exception {
        // 创建执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        
        // 设置并行度
        env.setParallelism(3);
        
        // 启用检查点
        env.enableCheckpointing(60000); // 每分钟检查点
        
        logger.info("Starting Flink Recommendation Job...");
        
        // 创建Kafka源
        KafkaSource<UserBehavior> userBehaviorSource = createUserBehaviorSource();
        KafkaSource<Product> productSource = createProductSource();
        
        // 创建Kafka接收器
        KafkaSink<Recommendation> recommendationSink = createRecommendationSink();
        
        // 处理用户行为流
        DataStream<UserBehavior> userBehaviorStream = env
                .fromSource(userBehaviorSource, WatermarkStrategy.noWatermarks(), "User Behavior Source")
                .name("User Behavior Stream");
        
        // 处理商品事件流
        DataStream<Product> productStream = env
                .fromSource(productSource, WatermarkStrategy.noWatermarks(), "Product Source")
                .name("Product Stream");
        
        // 处理商品更新
        DataStream<String> productUpdateStream = productStream
                .map(new ProductUpdateFunction())
                .name("Product Update Stream");
        
        // 生成推荐
        DataStream<Recommendation> recommendationStream = userBehaviorStream
                .keyBy(UserBehavior::getUserId)
                .process(new RecommendationProcessFunction())
                .name("Recommendation Stream");
        
        // 输出推荐结果到Kafka
        recommendationStream.sinkTo(recommendationSink).name("Recommendation Sink");
        
        // 打印推荐结果到控制台（用于调试）
        recommendationStream.print("Recommendation");
        
        // 执行作业
        env.execute("E-commerce Recommendation System");
    }
    
    /**
     * 创建用户行为Kafka源
     */
    private static KafkaSource<UserBehavior> createUserBehaviorSource() {
        return KafkaSource.<UserBehavior>builder()
                .setBootstrapServers(KAFKA_BOOTSTRAP_SERVERS)
                .setTopics(USER_BEHAVIOR_TOPIC)
                .setGroupId("flink-recommendation-group")
                .setStartingOffsets(OffsetsInitializer.latest())
                .setValueOnlyDeserializer(new JsonDeserializationSchema<>(UserBehavior.class))
                .build();
    }
    
    /**
     * 创建商品事件Kafka源
     */
    private static KafkaSource<Product> createProductSource() {
        return KafkaSource.<Product>builder()
                .setBootstrapServers(KAFKA_BOOTSTRAP_SERVERS)
                .setTopics(PRODUCT_EVENTS_TOPIC)
                .setGroupId("flink-product-group")
                .setStartingOffsets(OffsetsInitializer.latest())
                .setValueOnlyDeserializer(new JsonDeserializationSchema<>(Product.class))
                .build();
    }
    
    /**
     * 创建推荐结果Kafka接收器
     */
    private static KafkaSink<Recommendation> createRecommendationSink() {
        return KafkaSink.<Recommendation>builder()
                .setBootstrapServers(KAFKA_BOOTSTRAP_SERVERS)
                .setRecordSerializer(KafkaRecordSerializationSchema.builder()
                        .setTopic(RECOMMENDATIONS_TOPIC)
                        .setValueSerializationSchema(new JsonSerializationSchema<>())
                        .build())
                .build();
    }
    
    /**
     * 商品更新处理函数
     */
    public static class ProductUpdateFunction extends RichMapFunction<Product, String> {
        
        private transient RecommendationEngine recommendationEngine;
        
        @Override
        public void open(Configuration parameters) throws Exception {
            super.open(parameters);
            recommendationEngine = new RecommendationEngine();
        }
        
        @Override
        public String map(Product product) throws Exception {
            recommendationEngine.updateProduct(product);
            logger.info("Updated product in recommendation engine: {}", product.getProductId());
            return product.getProductId();
        }
    }
    
    /**
     * 推荐处理函数
     */
    public static class RecommendationProcessFunction extends KeyedProcessFunction<String, UserBehavior, Recommendation> {
        
        private transient ValueState<RecommendationEngine> engineState;
        
        @Override
        public void open(Configuration parameters) throws Exception {
            super.open(parameters);
            
            ValueStateDescriptor<RecommendationEngine> engineDescriptor = 
                new ValueStateDescriptor<>("recommendation-engine", RecommendationEngine.class);
            engineState = getRuntimeContext().getState(engineDescriptor);
        }
        
        @Override
        public void processElement(UserBehavior behavior, Context context, Collector<Recommendation> out) throws Exception {
            // 获取或创建推荐引擎
            RecommendationEngine engine = engineState.value();
            if (engine == null) {
                engine = new RecommendationEngine();
                engineState.update(engine);
            }
            
            logger.info("Processing user behavior: {} - {} - {}", 
                       behavior.getUserId(), behavior.getProductId(), behavior.getBehaviorType());
            
            // 处理用户行为并生成推荐
            Recommendation recommendation = engine.processUserBehavior(behavior);
            
            if (recommendation != null && recommendation.getRecommendedProducts() != null 
                && !recommendation.getRecommendedProducts().isEmpty()) {
                
                recommendation.setSessionId(behavior.getSessionId());
                out.collect(recommendation);
                
                logger.info("Generated recommendation for user: {}, products count: {}", 
                           recommendation.getUserId(), recommendation.getRecommendedProducts().size());
            }
            
            // 设置定时器，定期清理过期数据（可选）
            context.timerService().registerProcessingTimeTimer(
                context.timerService().currentProcessingTime() + Duration.ofHours(1).toMillis());
        }
        
        @Override
        public void onTimer(long timestamp, OnTimerContext ctx, Collector<Recommendation> out) throws Exception {
            // 定期清理逻辑（可以在这里实现数据清理）
            logger.debug("Timer triggered for user: {}", ctx.getCurrentKey());
        }
    }
}
