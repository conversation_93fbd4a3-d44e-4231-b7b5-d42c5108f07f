version: '3.8'

services:
  # Zoo<PERSON>eeper服务 - 使用国内镜像
  zookeeper:
    image: registry.cn-hangzhou.aliyuncs.com/zhengqing/zookeeper:3.8.0
    hostname: zookeeper
    container_name: zookeeper
    ports:
      - "2181:2181"
    environment:
      ZOO_MY_ID: 1
      ZOO_SERVERS: server.1=0.0.0.0:2888:3888;2181
    volumes:
      - zookeeper-data:/data
      - zookeeper-logs:/datalog
    restart: unless-stopped

  # Kafka Broker 1
  kafka1:
    image: registry.cn-hangzhou.aliyuncs.com/zhengqing/kafka:3.4.0
    hostname: kafka1
    container_name: kafka1
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka1:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:29092,PLAINTEXT_HOST://0.0.0.0:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 3
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 2
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 3
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_NUM_PARTITIONS: 3
    volumes:
      - kafka1-data:/var/lib/kafka/data
    restart: unless-stopped

  # Kafka Broker 2
  kafka2:
    image: registry.cn-hangzhou.aliyuncs.com/zhengqing/kafka:3.4.0
    hostname: kafka2
    container_name: kafka2
    depends_on:
      - zookeeper
    ports:
      - "9093:9093"
    environment:
      KAFKA_BROKER_ID: 2
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka2:29093,PLAINTEXT_HOST://localhost:9093
      KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:29093,PLAINTEXT_HOST://0.0.0.0:9093
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 3
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 2
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 3
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_NUM_PARTITIONS: 3
    volumes:
      - kafka2-data:/var/lib/kafka/data
    restart: unless-stopped

  # Kafka Broker 3
  kafka3:
    image: registry.cn-hangzhou.aliyuncs.com/zhengqing/kafka:3.4.0
    hostname: kafka3
    container_name: kafka3
    depends_on:
      - zookeeper
    ports:
      - "9094:9094"
    environment:
      KAFKA_BROKER_ID: 3
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka3:29094,PLAINTEXT_HOST://localhost:9094
      KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:29094,PLAINTEXT_HOST://0.0.0.0:9094
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 3
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 2
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 3
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_NUM_PARTITIONS: 3
    volumes:
      - kafka3-data:/var/lib/kafka/data
    restart: unless-stopped

  # Redis缓存 - 使用官方镜像（通常能正常下载）
  redis:
    image: redis:7-alpine
    hostname: redis
    container_name: redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Flink JobManager - 使用阿里云镜像
  jobmanager:
    image: registry.cn-hangzhou.aliyuncs.com/zhengqing/flink:1.17.1
    hostname: jobmanager
    container_name: jobmanager
    ports:
      - "8081:8081"
    command: jobmanager
    environment:
      - |
        FLINK_PROPERTIES=
        jobmanager.rpc.address: jobmanager
        parallelism.default: 2
        taskmanager.numberOfTaskSlots: 2
        jobmanager.memory.process.size: 1600m
        taskmanager.memory.process.size: 1728m
    volumes:
      - flink-data:/opt/flink/data
    restart: unless-stopped

  # Flink TaskManager 1
  taskmanager1:
    image: registry.cn-hangzhou.aliyuncs.com/zhengqing/flink:1.17.1
    hostname: taskmanager1
    container_name: taskmanager1
    depends_on:
      - jobmanager
    command: taskmanager
    environment:
      - |
        FLINK_PROPERTIES=
        jobmanager.rpc.address: jobmanager
        taskmanager.numberOfTaskSlots: 2
        parallelism.default: 2
        taskmanager.memory.process.size: 1728m
    volumes:
      - flink-data:/opt/flink/data
    restart: unless-stopped

  # Flink TaskManager 2
  taskmanager2:
    image: registry.cn-hangzhou.aliyuncs.com/zhengqing/flink:1.17.1
    hostname: taskmanager2
    container_name: taskmanager2
    depends_on:
      - jobmanager
    command: taskmanager
    environment:
      - |
        FLINK_PROPERTIES=
        jobmanager.rpc.address: jobmanager
        taskmanager.numberOfTaskSlots: 2
        parallelism.default: 2
        taskmanager.memory.process.size: 1728m
    volumes:
      - flink-data:/opt/flink/data
    restart: unless-stopped

  # Flink TaskManager 3
  taskmanager3:
    image: registry.cn-hangzhou.aliyuncs.com/zhengqing/flink:1.17.1
    hostname: taskmanager3
    container_name: taskmanager3
    depends_on:
      - jobmanager
    command: taskmanager
    environment:
      - |
        FLINK_PROPERTIES=
        jobmanager.rpc.address: jobmanager
        taskmanager.numberOfTaskSlots: 2
        parallelism.default: 2
        taskmanager.memory.process.size: 1728m
    volumes:
      - flink-data:/opt/flink/data
    restart: unless-stopped

volumes:
  zookeeper-data:
  zookeeper-logs:
  kafka1-data:
  kafka2-data:
  kafka3-data:
  redis-data:
  flink-data:

networks:
  default:
    name: ecommerce-network
