package com.ecommerce.flink.serialization;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.apache.flink.api.common.serialization.SerializationSchema;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * JSON序列化Schema
 */
public class JsonSerializationSchema<T> implements SerializationSchema<T> {
    
    private static final Logger logger = LoggerFactory.getLogger(JsonSerializationSchema.class);
    
    private transient ObjectMapper objectMapper;
    
    @Override
    public void open(InitializationContext context) throws Exception {
        objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
    }
    
    @Override
    public byte[] serialize(T element) {
        if (element == null) {
            return new byte[0];
        }
        
        try {
            String jsonString = objectMapper.writeValueAsString(element);
            logger.debug("Serializing to JSON: {}", jsonString);
            return jsonString.getBytes();
        } catch (Exception e) {
            logger.error("Failed to serialize object to JSON: {}", element, e);
            return new byte[0];
        }
    }
}
