# 分布式实时电商推荐系统

## 项目概述
基于Apache Kafka和Apache Flink构建的分布式实时电商推荐系统，实现用户行为数据的实时处理和个性化推荐。

## 系统架构
```
用户操作 -> 消息源软件 -> Kafka -> Flink推荐引擎 -> Kafka -> 推荐结果展示
```

## 技术栈
- **消息队列**: Apache Kafka 3.6.0
- **流处理引擎**: Apache Flink 1.17
- **后端**: Spring Boot + Java 8/11
- **前端**: React + TypeScript
- **数据库**: Redis (缓存)

## 集群配置
### 3节点集群部署
- **Hadoop01**: JobManager + TaskManager + Kafka Broker + ZooKeeper
- **Hadoop02**: TaskManager + Kafka Broker
- **Hadoop03**: TaskManager + Kafka Broker

## 项目结构
```
ecommerce-recommendation-system/
├── message-source/          # 消息源软件
├── flink-recommendation/    # Flink推荐引擎
├── kafka-utils/            # Kafka工具类
├── backend/                # 后端API服务
├── frontend/               # 前端界面
├── scripts/                # 部署脚本
└── docs/                   # 文档
```

## 快速开始
1. 启动Kafka集群: `./scripts/start-kafka.sh`
2. 启动Flink集群: `./scripts/start-flink.sh`
3. 启动后端服务: `./scripts/start-backend.sh`
4. 启动前端服务: `./scripts/start-frontend.sh`
5. 运行消息源软件: `./scripts/start-message-source.sh`

## 功能特性
- ✅ 实时用户行为数据采集
- ✅ 基于协同过滤的推荐算法
- ✅ 分布式消息队列处理
- ✅ 实时流数据处理
- ✅ Web界面管理和监控
- ✅ 高可用性和容错机制

## 开发者指南
详见 [docs/developer-guide.md](docs/developer-guide.md)
