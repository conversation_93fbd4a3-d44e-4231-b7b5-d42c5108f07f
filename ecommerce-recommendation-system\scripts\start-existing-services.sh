#!/bin/bash

# 使用现有Kafka和Flink集群启动服务

set -e

echo "=== 启动现有的Kafka和Flink集群 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 项目根目录
PROJECT_ROOT=$(pwd)
KAFKA_DIR="../kafka-cluster"
FLINK_DIR="../flink-cluster"

# 检查现有安装
check_existing_installations() {
    log_info "检查现有安装..."
    
    if [ ! -d "$KAFKA_DIR" ]; then
        log_error "未找到Kafka集群目录: $KAFKA_DIR"
        exit 1
    fi
    
    if [ ! -d "$FLINK_DIR" ]; then
        log_error "未找到Flink集群目录: $FLINK_DIR"
        exit 1
    fi
    
    log_info "找到现有的Kafka和Flink安装"
}

# 启动Redis
start_redis() {
    log_info "启动Redis..."
    
    if pgrep redis-server > /dev/null; then
        log_info "Redis已在运行"
    else
        redis-server --daemonize yes --port 6379
        sleep 2
        if pgrep redis-server > /dev/null; then
            log_info "Redis启动成功"
        else
            log_error "Redis启动失败"
            exit 1
        fi
    fi
}

# 启动ZooKeeper
start_zookeeper() {
    log_info "启动ZooKeeper..."
    
    cd "$KAFKA_DIR"
    
    # 检查是否已在运行
    if pgrep -f "zookeeper" > /dev/null; then
        log_info "ZooKeeper已在运行"
        cd "$PROJECT_ROOT"
        return
    fi
    
    # 检查Kafka安装
    if [ ! -f "../kafka_2.13-3.6.0/bin/zookeeper-server-start.sh" ]; then
        log_error "未找到Kafka安装，请先运行完整安装脚本"
        exit 1
    fi
    
    # 启动ZooKeeper
    nohup ../kafka_2.13-3.6.0/bin/zookeeper-server-start.sh config/zookeeper.properties > logs/zookeeper.log 2>&1 &
    
    sleep 5
    
    if pgrep -f "zookeeper" > /dev/null; then
        log_info "ZooKeeper启动成功"
    else
        log_error "ZooKeeper启动失败，请检查日志: $KAFKA_DIR/logs/zookeeper.log"
        exit 1
    fi
    
    cd "$PROJECT_ROOT"
}

# 启动Kafka集群
start_kafka_cluster() {
    log_info "启动Kafka集群..."
    
    cd "$KAFKA_DIR"
    
    # 检查是否已在运行
    if pgrep -f "kafka" > /dev/null; then
        log_info "Kafka集群已在运行"
        cd "$PROJECT_ROOT"
        return
    fi
    
    # 启动三个Kafka节点
    for node in hadoop01 hadoop02 hadoop03; do
        log_info "启动Kafka节点: $node"
        nohup ../kafka_2.13-3.6.0/bin/kafka-server-start.sh config/server-${node}.properties > logs/kafka-${node}.log 2>&1 &
        sleep 3
    done
    
    sleep 10
    
    # 验证启动
    if pgrep -f "kafka" > /dev/null; then
        log_info "Kafka集群启动成功"
    else
        log_error "Kafka集群启动失败，请检查日志"
        exit 1
    fi
    
    cd "$PROJECT_ROOT"
}

# 启动Flink集群
start_flink_cluster() {
    log_info "启动Flink集群..."
    
    cd "$FLINK_DIR"
    
    # 检查是否已在运行
    if pgrep -f "flink" > /dev/null; then
        log_info "Flink集群已在运行"
        cd "$PROJECT_ROOT"
        return
    fi
    
    # 检查Flink安装
    if [ ! -f "../flink-1.17.2/bin/start-cluster.sh" ]; then
        # 解压Flink
        if [ -f "../flink-1.17.2-bin-scala_2.12.tgz" ]; then
            log_info "解压Flink..."
            cd ..
            tar -xzf flink-1.17.2-bin-scala_2.12.tgz
            cd flink-cluster
        else
            log_error "未找到Flink安装包"
            exit 1
        fi
    fi
    
    # 启动Flink集群
    ../flink-1.17.2/bin/start-cluster.sh
    
    sleep 5
    
    if pgrep -f "flink" > /dev/null; then
        log_info "Flink集群启动成功"
    else
        log_error "Flink集群启动失败"
        exit 1
    fi
    
    cd "$PROJECT_ROOT"
}

# 创建Kafka主题
create_kafka_topics() {
    log_info "创建Kafka主题..."
    
    cd "$KAFKA_DIR"
    
    # 等待Kafka完全启动
    sleep 5
    
    # 创建主题
    ../kafka_2.13-3.6.0/bin/kafka-topics.sh --create --topic user-behavior --bootstrap-server localhost:9092 --partitions 3 --replication-factor 3 --if-not-exists
    ../kafka_2.13-3.6.0/bin/kafka-topics.sh --create --topic product-events --bootstrap-server localhost:9092 --partitions 3 --replication-factor 3 --if-not-exists
    ../kafka_2.13-3.6.0/bin/kafka-topics.sh --create --topic recommendations --bootstrap-server localhost:9092 --partitions 3 --replication-factor 3 --if-not-exists
    
    log_info "Kafka主题创建完成"
    
    cd "$PROJECT_ROOT"
}

# 构建项目
build_projects() {
    log_info "构建Java项目..."
    
    # 构建消息源软件
    if [ -d "message-source" ]; then
        log_info "构建消息源软件..."
        cd message-source
        mvn clean package -DskipTests -q
        cd ..
    fi
    
    # 构建Flink推荐引擎
    if [ -d "flink-recommendation" ]; then
        log_info "构建Flink推荐引擎..."
        cd flink-recommendation
        mvn clean package -DskipTests -q
        cd ..
    fi
    
    log_info "项目构建完成"
}

# 显示服务状态
show_status() {
    log_info "=== 服务状态 ==="
    
    echo "Redis: $(pgrep redis-server > /dev/null && echo "运行中" || echo "未运行")"
    echo "ZooKeeper: $(pgrep -f zookeeper > /dev/null && echo "运行中" || echo "未运行")"
    echo "Kafka: $(pgrep -f kafka > /dev/null && echo "运行中" || echo "未运行")"
    echo "Flink: $(pgrep -f flink > /dev/null && echo "运行中" || echo "未运行")"
    
    echo ""
    echo "Web界面:"
    echo "  Flink Dashboard: http://localhost:8081"
    echo ""
    echo "端口:"
    echo "  Kafka: 9092, 9093, 9094"
    echo "  ZooKeeper: 2181"
    echo "  Redis: 6379"
    echo "  Flink Web UI: 8081"
}

# 主函数
main() {
    log_info "使用现有安装启动服务..."
    
    check_existing_installations
    start_redis
    start_zookeeper
    start_kafka_cluster
    start_flink_cluster
    create_kafka_topics
    build_projects
    show_status
    
    log_info "=== 所有服务启动完成 ==="
    log_info ""
    log_info "下一步:"
    log_info "1. 启动消息源软件: ./scripts/start-message-source.sh"
    log_info "2. 提交Flink作业: ./scripts/submit-flink-job.sh"
    log_info "3. 启动后端API: cd backend && mvn spring-boot:run"
    log_info "4. 启动前端界面: cd frontend && npm start"
}

# 执行主函数
main "$@"
