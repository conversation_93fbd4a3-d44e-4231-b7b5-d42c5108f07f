body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* 自定义样式 */
.ant-layout {
  background: #f0f2f5;
}

.ant-layout-sider {
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
}

.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ant-card-head-title {
  font-weight: 600;
}

.ant-statistic-title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}

.ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-layout-sider {
    position: fixed !important;
    height: 100vh;
    z-index: 999;
  }
  
  .ant-layout-content {
    margin-left: 0 !important;
  }
}

/* 图表样式 */
.recharts-wrapper {
  font-size: 12px;
}

.recharts-cartesian-axis-tick-value {
  font-size: 12px;
  fill: #666;
}

.recharts-legend-wrapper {
  font-size: 12px;
}

/* 表格样式 */
.ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}

.ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

/* 按钮样式 */
.ant-btn {
  border-radius: 6px;
}

.ant-btn-primary {
  background: #1890ff;
  border-color: #1890ff;
}

.ant-btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* 标签样式 */
.ant-tag {
  border-radius: 4px;
  font-size: 12px;
}

/* 进度条样式 */
.ant-progress-text {
  font-size: 12px;
}

/* 模态框样式 */
.ant-modal-header {
  border-radius: 8px 8px 0 0;
}

.ant-modal-content {
  border-radius: 8px;
}

/* 表单样式 */
.ant-form-item-label > label {
  font-weight: 500;
}

/* 菜单样式 */
.ant-menu-item-selected {
  background-color: #e6f7ff !important;
}

.ant-menu-item-selected a {
  color: #1890ff !important;
}

/* 加载样式 */
.ant-spin-dot-item {
  background-color: #1890ff;
}

/* 警告框样式 */
.ant-alert {
  border-radius: 6px;
}

/* 统计数字样式 */
.ant-statistic {
  text-align: center;
}

.ant-statistic-content-value {
  font-family: 'Roboto', sans-serif;
}

/* 卡片悬停效果 */
.ant-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: box-shadow 0.3s ease;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
