#!/bin/bash

# 分布式实时电商推荐系统环境配置脚本

set -e

echo "=== 开始配置分布式实时电商推荐系统环境 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否在WSL环境中
check_wsl() {
    if grep -q Microsoft /proc/version; then
        log_info "检测到WSL环境"
        export WSL_ENV=true
    else
        log_info "检测到原生Linux环境"
        export WSL_ENV=false
    fi
}

# 检查Java环境
check_java() {
    log_info "检查Java环境..."
    
    if command -v java &> /dev/null; then
        JAVA_VERSION=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}')
        log_info "Java版本: $JAVA_VERSION"
        
        # 检查Java版本是否为8或11
        if [[ $JAVA_VERSION == 1.8* ]] || [[ $JAVA_VERSION == 11* ]]; then
            log_info "Java版本符合要求"
        else
            log_warn "建议使用Java 8或Java 11"
        fi
    else
        log_error "未找到Java，请先安装Java 8或Java 11"
        exit 1
    fi
}

# 安装依赖
install_dependencies() {
    log_info "安装系统依赖..."
    
    # 更新包管理器
    sudo apt update
    
    # 安装必要的工具
    sudo apt install -y wget curl unzip net-tools
    
    # 安装Maven（如果没有）
    if ! command -v mvn &> /dev/null; then
        log_info "安装Maven..."
        sudo apt install -y maven
    else
        log_info "Maven已安装: $(mvn -version | head -n 1)"
    fi
    
    # 安装Docker（如果没有）
    if ! command -v docker &> /dev/null; then
        log_info "安装Docker..."
        curl -fsSL https://get.docker.com -o get-docker.sh
        sudo sh get-docker.sh
        sudo usermod -aG docker $USER
        log_warn "请重新登录以使Docker权限生效"
    else
        log_info "Docker已安装: $(docker --version)"
    fi
    
    # 安装Docker Compose（如果没有）
    if ! command -v docker-compose &> /dev/null; then
        log_info "安装Docker Compose..."
        sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
        sudo chmod +x /usr/local/bin/docker-compose
    else
        log_info "Docker Compose已安装: $(docker-compose --version)"
    fi
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p logs
    mkdir -p data/kafka
    mkdir -p data/zookeeper
    mkdir -p data/flink
    mkdir -p data/redis
    
    log_info "目录创建完成"
}

# 配置主机名解析
configure_hosts() {
    log_info "配置主机名解析..."
    
    # 检查是否已配置
    if ! grep -q "hadoop01" /etc/hosts; then
        log_info "添加主机名解析到/etc/hosts..."
        
        cat << EOF | sudo tee -a /etc/hosts

# Hadoop集群节点
127.0.0.1 hadoop01
127.0.0.1 hadoop02
127.0.0.1 hadoop03
EOF
        
        log_info "主机名解析配置完成"
    else
        log_info "主机名解析已配置"
    fi
}

# 设置环境变量
setup_environment_variables() {
    log_info "设置环境变量..."
    
    # 创建环境变量文件
    cat << EOF > .env
# Kafka配置
KAFKA_BOOTSTRAP_SERVERS=localhost:9092,localhost:9093,localhost:9094
KAFKA_ZOOKEEPER_CONNECT=localhost:2181

# Flink配置
FLINK_JOBMANAGER_RPC_ADDRESS=localhost
FLINK_JOBMANAGER_RPC_PORT=6123
FLINK_JOBMANAGER_WEB_PORT=8081

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379

# 应用配置
APP_ENV=development
LOG_LEVEL=INFO
EOF
    
    log_info "环境变量文件创建完成: .env"
}

# 构建项目
build_projects() {
    log_info "构建项目..."
    
    # 构建消息源软件
    log_info "构建消息源软件..."
    cd message-source
    mvn clean package -DskipTests
    cd ..
    
    # 构建Flink推荐引擎
    log_info "构建Flink推荐引擎..."
    cd flink-recommendation
    mvn clean package -DskipTests
    cd ..
    
    log_info "项目构建完成"
}

# 创建Kafka主题
create_kafka_topics() {
    log_info "等待Kafka启动..."
    sleep 30
    
    log_info "创建Kafka主题..."
    
    # 用户行为主题
    docker exec kafka1 kafka-topics --create \
        --topic user-behavior \
        --bootstrap-server localhost:9092 \
        --partitions 3 \
        --replication-factor 3 \
        --if-not-exists
    
    # 商品事件主题
    docker exec kafka1 kafka-topics --create \
        --topic product-events \
        --bootstrap-server localhost:9092 \
        --partitions 3 \
        --replication-factor 3 \
        --if-not-exists
    
    # 推荐结果主题
    docker exec kafka1 kafka-topics --create \
        --topic recommendations \
        --bootstrap-server localhost:9092 \
        --partitions 3 \
        --replication-factor 3 \
        --if-not-exists
    
    log_info "Kafka主题创建完成"
}

# 主函数
main() {
    log_info "开始环境配置..."
    
    check_wsl
    check_java
    install_dependencies
    create_directories
    configure_hosts
    setup_environment_variables
    
    log_info "启动Docker服务..."
    if [ "$WSL_ENV" = true ]; then
        sudo service docker start
    fi
    
    log_info "启动基础设施..."
    docker-compose up -d
    
    build_projects
    create_kafka_topics
    
    log_info "=== 环境配置完成 ==="
    log_info "请运行以下命令查看服务状态:"
    log_info "  docker-compose ps"
    log_info ""
    log_info "Web界面访问地址:"
    log_info "  Flink Dashboard: http://localhost:8081"
    log_info "  Kafka Manager: 可以使用kafka-topics命令管理"
    log_info ""
    log_info "启动应用程序:"
    log_info "  ./scripts/start-message-source.sh"
    log_info "  ./scripts/submit-flink-job.sh"
}

# 执行主函数
main "$@"
