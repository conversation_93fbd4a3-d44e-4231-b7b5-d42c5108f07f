#!/bin/bash

# 修正配置并启动服务

set -e

echo "=== 修正配置并启动分布式电商推荐系统 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 项目根目录
PROJECT_ROOT="/mnt/c/Users/<USER>/Desktop/second"
KAFKA_DIR="$PROJECT_ROOT/kafka-cluster"
FLINK_DIR="$PROJECT_ROOT/flink-cluster"

# 修正主机名映射
fix_hosts() {
    log_info "修正主机名映射..."
    
    # 备份原hosts文件
    sudo cp /etc/hosts /etc/hosts.backup
    
    # 移除旧的hadoop映射
    sudo sed -i '/hadoop01/d' /etc/hosts
    sudo sed -i '/hadoop02/d' /etc/hosts
    sudo sed -i '/hadoop03/d' /etc/hosts
    
    # 添加正确的映射（单机模拟分布式）
    cat << EOF | sudo tee -a /etc/hosts

# Hadoop集群节点（单机模拟）
127.0.0.1 hadoop01
127.0.0.1 hadoop02  
127.0.0.1 hadoop03
EOF
    
    log_info "主机名映射已修正"
}

# 检查并修正Kafka配置
fix_kafka_config() {
    log_info "检查Kafka配置..."
    
    if [ ! -d "$KAFKA_DIR" ]; then
        log_error "未找到Kafka目录: $KAFKA_DIR"
        return 1
    fi
    
    # 检查配置文件
    for node in hadoop01 hadoop02 hadoop03; do
        config_file="$KAFKA_DIR/config/server-${node}.properties"
        if [ ! -f "$config_file" ]; then
            log_warn "未找到配置文件: $config_file"
            create_kafka_config "$node"
        fi
    done
    
    log_info "Kafka配置检查完成"
}

# 创建Kafka配置
create_kafka_config() {
    local node=$1
    local broker_id
    local port
    
    case $node in
        hadoop01) broker_id=1; port=9092 ;;
        hadoop02) broker_id=2; port=9093 ;;
        hadoop03) broker_id=3; port=9094 ;;
    esac
    
    log_info "创建Kafka配置: $node"
    
    cat > "$KAFKA_DIR/config/server-${node}.properties" << EOF
# Kafka配置 - $node
broker.id=$broker_id
listeners=PLAINTEXT://localhost:$port
advertised.listeners=PLAINTEXT://localhost:$port
log.dirs=$KAFKA_DIR/$node/kafka-logs
num.network.threads=3
num.io.threads=8
socket.send.buffer.bytes=102400
socket.receive.buffer.bytes=102400
socket.request.max.bytes=104857600
num.partitions=3
num.recovery.threads.per.data.dir=1
offsets.topic.replication.factor=3
transaction.state.log.replication.factor=3
transaction.state.log.min.isr=2
log.retention.hours=168
log.segment.bytes=1073741824
log.retention.check.interval.ms=300000
zookeeper.connect=localhost:2181
zookeeper.connection.timeout.ms=18000
group.initial.rebalance.delay.ms=0
EOF
}

# 创建ZooKeeper配置
create_zookeeper_config() {
    log_info "创建ZooKeeper配置..."
    
    cat > "$KAFKA_DIR/config/zookeeper.properties" << EOF
# ZooKeeper配置
dataDir=$KAFKA_DIR/zookeeper
clientPort=2181
maxClientCnxns=0
admin.enableServer=false
tickTime=2000
initLimit=10
syncLimit=5
EOF
}

# 检查并安装Kafka
check_kafka_installation() {
    log_info "检查Kafka安装..."
    
    KAFKA_HOME="$PROJECT_ROOT/kafka_2.13-3.6.0"
    
    if [ ! -d "$KAFKA_HOME" ]; then
        log_info "Kafka未安装，开始安装..."
        
        cd "$PROJECT_ROOT"
        
        if [ ! -f "kafka_2.13-3.6.0.tgz" ]; then
            log_info "下载Kafka..."
            wget -q https://downloads.apache.org/kafka/3.6.0/kafka_2.13-3.6.0.tgz
        fi
        
        log_info "解压Kafka..."
        tar -xzf kafka_2.13-3.6.0.tgz
        
        log_info "Kafka安装完成"
    else
        log_info "Kafka已安装: $KAFKA_HOME"
    fi
}

# 检查并安装Flink
check_flink_installation() {
    log_info "检查Flink安装..."
    
    FLINK_HOME="$PROJECT_ROOT/flink-1.17.2"
    
    if [ ! -d "$FLINK_HOME" ]; then
        log_info "Flink未安装，开始安装..."
        
        cd "$PROJECT_ROOT"
        
        if [ -f "flink-1.17.2-bin-scala_2.12.tgz" ]; then
            log_info "解压Flink..."
            tar -xzf flink-1.17.2-bin-scala_2.12.tgz
        else
            log_info "下载Flink..."
            wget -q https://downloads.apache.org/flink/flink-1.17.2/flink-1.17.2-bin-scala_2.12.tgz
            tar -xzf flink-1.17.2-bin-scala_2.12.tgz
        fi
        
        log_info "Flink安装完成"
    else
        log_info "Flink已安装: $FLINK_HOME"
    fi
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    KAFKA_HOME="$PROJECT_ROOT/kafka_2.13-3.6.0"
    FLINK_HOME="$PROJECT_ROOT/flink-1.17.2"
    
    # 创建必要目录
    mkdir -p "$KAFKA_DIR/logs"
    mkdir -p "$KAFKA_DIR/zookeeper"
    for node in hadoop01 hadoop02 hadoop03; do
        mkdir -p "$KAFKA_DIR/$node/kafka-logs"
    done
    
    # 启动Redis
    log_info "启动Redis..."
    if ! pgrep redis-server > /dev/null; then
        redis-server --daemonize yes --port 6379
        sleep 2
    fi
    
    # 启动ZooKeeper
    log_info "启动ZooKeeper..."
    if ! pgrep -f "zookeeper" > /dev/null; then
        cd "$KAFKA_DIR"
        nohup "$KAFKA_HOME/bin/zookeeper-server-start.sh" config/zookeeper.properties > logs/zookeeper.log 2>&1 &
        sleep 5
        cd "$PROJECT_ROOT"
    fi
    
    # 启动Kafka集群
    log_info "启动Kafka集群..."
    if ! pgrep -f "kafka" > /dev/null; then
        cd "$KAFKA_DIR"
        for node in hadoop01 hadoop02 hadoop03; do
            log_info "启动Kafka节点: $node"
            nohup "$KAFKA_HOME/bin/kafka-server-start.sh" config/server-${node}.properties > logs/kafka-${node}.log 2>&1 &
            sleep 3
        done
        sleep 10
        cd "$PROJECT_ROOT"
    fi
    
    # 启动Flink集群
    log_info "启动Flink集群..."
    if ! pgrep -f "flink" > /dev/null; then
        cd "$FLINK_HOME"
        ./bin/start-cluster.sh
        sleep 5
        cd "$PROJECT_ROOT"
    fi
}

# 创建Kafka主题
create_topics() {
    log_info "创建Kafka主题..."
    
    KAFKA_HOME="$PROJECT_ROOT/kafka_2.13-3.6.0"
    
    # 等待Kafka启动
    sleep 5
    
    "$KAFKA_HOME/bin/kafka-topics.sh" --create --topic user-behavior --bootstrap-server localhost:9092 --partitions 3 --replication-factor 3 --if-not-exists
    "$KAFKA_HOME/bin/kafka-topics.sh" --create --topic product-events --bootstrap-server localhost:9092 --partitions 3 --replication-factor 3 --if-not-exists
    "$KAFKA_HOME/bin/kafka-topics.sh" --create --topic recommendations --bootstrap-server localhost:9092 --partitions 3 --replication-factor 3 --if-not-exists
    
    log_info "Kafka主题创建完成"
}

# 构建项目
build_projects() {
    log_info "构建Java项目..."
    
    cd "$PROJECT_ROOT/ecommerce-recommendation-system"
    
    # 构建消息源软件
    if [ -d "message-source" ]; then
        log_info "构建消息源软件..."
        cd message-source
        mvn clean package -DskipTests -q
        cd ..
    fi
    
    # 构建Flink推荐引擎
    if [ -d "flink-recommendation" ]; then
        log_info "构建Flink推荐引擎..."
        cd flink-recommendation
        mvn clean package -DskipTests -q
        cd ..
    fi
    
    log_info "项目构建完成"
}

# 显示状态
show_status() {
    log_info "=== 服务状态 ==="
    
    echo "Redis: $(pgrep redis-server > /dev/null && echo "✅ 运行中" || echo "❌ 未运行")"
    echo "ZooKeeper: $(pgrep -f zookeeper > /dev/null && echo "✅ 运行中" || echo "❌ 未运行")"
    echo "Kafka: $(pgrep -f kafka > /dev/null && echo "✅ 运行中" || echo "❌ 未运行")"
    echo "Flink: $(pgrep -f flink > /dev/null && echo "✅ 运行中" || echo "❌ 未运行")"
    
    echo ""
    echo "🌐 Web界面:"
    echo "  Flink Dashboard: http://localhost:8081"
    echo ""
    echo "🔌 端口:"
    echo "  Kafka: 9092, 9093, 9094"
    echo "  ZooKeeper: 2181"
    echo "  Redis: 6379"
    echo "  Flink Web UI: 8081"
    echo ""
    echo "📁 安装目录:"
    echo "  Kafka: $PROJECT_ROOT/kafka_2.13-3.6.0"
    echo "  Flink: $PROJECT_ROOT/flink-1.17.2"
    echo "  配置: $PROJECT_ROOT/kafka-cluster"
}

# 主函数
main() {
    log_info "开始修正配置并启动服务..."
    
    fix_hosts
    fix_kafka_config
    create_zookeeper_config
    check_kafka_installation
    check_flink_installation
    start_services
    create_topics
    build_projects
    show_status
    
    log_info "=== 系统启动完成 ==="
    log_info ""
    log_info "🚀 下一步:"
    log_info "1. 启动消息源软件: cd ecommerce-recommendation-system && ./scripts/start-message-source.sh"
    log_info "2. 提交Flink作业: ./scripts/submit-flink-job.sh"
    log_info "3. 启动后端API: cd backend && mvn spring-boot:run"
    log_info "4. 启动前端界面: cd frontend && npm install && npm start"
}

# 执行主函数
main "$@"
