package com.ecommerce.flink.serialization;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * JSON反序列化Schema
 */
public class JsonDeserializationSchema<T> implements DeserializationSchema<T> {
    
    private static final Logger logger = LoggerFactory.getLogger(JsonDeserializationSchema.class);
    
    private final Class<T> clazz;
    private transient ObjectMapper objectMapper;
    
    public JsonDeserializationSchema(Class<T> clazz) {
        this.clazz = clazz;
    }
    
    @Override
    public void open(InitializationContext context) throws Exception {
        objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
    }
    
    @Override
    public T deserialize(byte[] message) throws IOException {
        if (message == null || message.length == 0) {
            return null;
        }
        
        try {
            String jsonString = new String(message);
            logger.debug("Deserializing JSON: {}", jsonString);
            return objectMapper.readValue(jsonString, clazz);
        } catch (Exception e) {
            logger.error("Failed to deserialize JSON message: {}", new String(message), e);
            return null;
        }
    }
    
    @Override
    public boolean isEndOfStream(T nextElement) {
        return false;
    }
    
    @Override
    public TypeInformation<T> getProducedType() {
        return TypeInformation.of(clazz);
    }
}
