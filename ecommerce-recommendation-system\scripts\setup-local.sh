#!/bin/bash

# 本地安装脚本（不使用Docker）

set -e

echo "=== 本地安装分布式实时电商推荐系统 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 创建安装目录
INSTALL_DIR="$HOME/ecommerce-services"
mkdir -p "$INSTALL_DIR"
cd "$INSTALL_DIR"

# 下载和安装Kafka
install_kafka() {
    log_info "安装Kafka..."
    
    if [ ! -d "kafka_2.13-3.6.0" ]; then
        log_info "下载Kafka..."
        wget -q https://downloads.apache.org/kafka/3.6.0/kafka_2.13-3.6.0.tgz
        tar -xzf kafka_2.13-3.6.0.tgz
        rm kafka_2.13-3.6.0.tgz
    fi
    
    log_info "Kafka安装完成"
}

# 下载和安装Flink
install_flink() {
    log_info "安装Flink..."
    
    if [ ! -d "flink-1.17.2" ]; then
        log_info "下载Flink..."
        wget -q https://downloads.apache.org/flink/flink-1.17.2/flink-1.17.2-bin-scala_2.12.tgz
        tar -xzf flink-1.17.2-bin-scala_2.12.tgz
        rm flink-1.17.2-bin-scala_2.12.tgz
    fi
    
    log_info "Flink安装完成"
}

# 安装Redis
install_redis() {
    log_info "安装Redis..."
    
    if ! command -v redis-server &> /dev/null; then
        sudo apt update
        sudo apt install -y redis-server
    fi
    
    log_info "Redis安装完成"
}

# 配置Kafka
configure_kafka() {
    log_info "配置Kafka..."
    
    KAFKA_DIR="$INSTALL_DIR/kafka_2.13-3.6.0"
    
    # 创建配置目录
    mkdir -p "$KAFKA_DIR/config-cluster"
    
    # ZooKeeper配置
    cat > "$KAFKA_DIR/config-cluster/zookeeper.properties" << EOF
dataDir=/tmp/zookeeper
clientPort=2181
maxClientCnxns=0
admin.enableServer=false
EOF

    # Kafka Broker配置
    cat > "$KAFKA_DIR/config-cluster/server.properties" << EOF
broker.id=1
listeners=PLAINTEXT://localhost:9092
log.dirs=/tmp/kafka-logs
num.network.threads=3
num.io.threads=8
socket.send.buffer.bytes=102400
socket.receive.buffer.bytes=102400
socket.request.max.bytes=104857600
num.partitions=3
num.recovery.threads.per.data.dir=1
offsets.topic.replication.factor=1
transaction.state.log.replication.factor=1
transaction.state.log.min.isr=1
log.retention.hours=168
log.segment.bytes=1073741824
log.retention.check.interval.ms=300000
zookeeper.connect=localhost:2181
zookeeper.connection.timeout.ms=18000
group.initial.rebalance.delay.ms=0
EOF

    log_info "Kafka配置完成"
}

# 配置Flink
configure_flink() {
    log_info "配置Flink..."
    
    FLINK_DIR="$INSTALL_DIR/flink-1.17.2"
    
    # 修改Flink配置
    cat > "$FLINK_DIR/conf/flink-conf.yaml" << EOF
jobmanager.rpc.address: localhost
jobmanager.rpc.port: 6123
jobmanager.memory.process.size: 1600m
taskmanager.memory.process.size: 1728m
taskmanager.numberOfTaskSlots: 2
parallelism.default: 2
jobmanager.execution.failover-strategy: region
rest.port: 8081
rest.address: localhost
EOF

    log_info "Flink配置完成"
}

# 创建启动脚本
create_start_scripts() {
    log_info "创建启动脚本..."
    
    # ZooKeeper启动脚本
    cat > "$INSTALL_DIR/start-zookeeper.sh" << EOF
#!/bin/bash
cd "$INSTALL_DIR/kafka_2.13-3.6.0"
bin/zookeeper-server-start.sh config-cluster/zookeeper.properties
EOF

    # Kafka启动脚本
    cat > "$INSTALL_DIR/start-kafka.sh" << EOF
#!/bin/bash
cd "$INSTALL_DIR/kafka_2.13-3.6.0"
bin/kafka-server-start.sh config-cluster/server.properties
EOF

    # Flink启动脚本
    cat > "$INSTALL_DIR/start-flink.sh" << EOF
#!/bin/bash
cd "$INSTALL_DIR/flink-1.17.2"
bin/start-cluster.sh
EOF

    # Redis启动脚本
    cat > "$INSTALL_DIR/start-redis.sh" << EOF
#!/bin/bash
redis-server --daemonize yes --port 6379
EOF

    # 全部启动脚本
    cat > "$INSTALL_DIR/start-all.sh" << EOF
#!/bin/bash
echo "启动所有服务..."

echo "启动Redis..."
redis-server --daemonize yes --port 6379

echo "启动ZooKeeper..."
cd "$INSTALL_DIR/kafka_2.13-3.6.0"
nohup bin/zookeeper-server-start.sh config-cluster/zookeeper.properties > ../logs/zookeeper.log 2>&1 &
sleep 5

echo "启动Kafka..."
nohup bin/kafka-server-start.sh config-cluster/server.properties > ../logs/kafka.log 2>&1 &
sleep 10

echo "启动Flink..."
cd "$INSTALL_DIR/flink-1.17.2"
bin/start-cluster.sh

echo "创建Kafka主题..."
cd "$INSTALL_DIR/kafka_2.13-3.6.0"
bin/kafka-topics.sh --create --topic user-behavior --bootstrap-server localhost:9092 --partitions 3 --replication-factor 1 --if-not-exists
bin/kafka-topics.sh --create --topic product-events --bootstrap-server localhost:9092 --partitions 3 --replication-factor 1 --if-not-exists
bin/kafka-topics.sh --create --topic recommendations --bootstrap-server localhost:9092 --partitions 3 --replication-factor 1 --if-not-exists

echo "所有服务启动完成！"
echo "Flink Web UI: http://localhost:8081"
echo "Kafka端口: 9092"
echo "Redis端口: 6379"
EOF

    # 停止脚本
    cat > "$INSTALL_DIR/stop-all.sh" << EOF
#!/bin/bash
echo "停止所有服务..."

echo "停止Flink..."
cd "$INSTALL_DIR/flink-1.17.2"
bin/stop-cluster.sh

echo "停止Kafka..."
cd "$INSTALL_DIR/kafka_2.13-3.6.0"
bin/kafka-server-stop.sh

echo "停止ZooKeeper..."
bin/zookeeper-server-stop.sh

echo "停止Redis..."
redis-cli shutdown

echo "所有服务已停止！"
EOF

    # 添加执行权限
    chmod +x "$INSTALL_DIR"/*.sh
    
    # 创建日志目录
    mkdir -p "$INSTALL_DIR/logs"
    
    log_info "启动脚本创建完成"
}

# 构建项目
build_projects() {
    log_info "构建项目..."
    
    cd "$OLDPWD"  # 回到项目目录
    
    # 构建消息源软件
    log_info "构建消息源软件..."
    cd message-source
    mvn clean package -DskipTests -q
    cd ..
    
    # 构建Flink推荐引擎
    log_info "构建Flink推荐引擎..."
    cd flink-recommendation
    mvn clean package -DskipTests -q
    cd ..
    
    log_info "项目构建完成"
}

# 主函数
main() {
    log_info "开始本地安装..."
    
    install_redis
    install_kafka
    install_flink
    configure_kafka
    configure_flink
    create_start_scripts
    build_projects
    
    log_info "=== 本地安装完成 ==="
    log_info ""
    log_info "服务安装目录: $INSTALL_DIR"
    log_info ""
    log_info "启动所有服务:"
    log_info "  $INSTALL_DIR/start-all.sh"
    log_info ""
    log_info "停止所有服务:"
    log_info "  $INSTALL_DIR/stop-all.sh"
    log_info ""
    log_info "Web界面:"
    log_info "  Flink Dashboard: http://localhost:8081"
    log_info ""
    log_info "下一步:"
    log_info "1. 运行: $INSTALL_DIR/start-all.sh"
    log_info "2. 启动消息源软件: ./scripts/start-message-source.sh"
    log_info "3. 提交Flink作业: ./scripts/submit-flink-job.sh"
}

# 执行主函数
main "$@"
