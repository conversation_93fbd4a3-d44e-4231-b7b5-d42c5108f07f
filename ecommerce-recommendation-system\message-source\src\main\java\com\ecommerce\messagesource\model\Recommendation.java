package com.ecommerce.messagesource.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 推荐结果实体类
 */
public class Recommendation {
    
    @JsonProperty("userId")
    private String userId;
    
    @JsonProperty("recommendedProducts")
    private List<RecommendedProduct> recommendedProducts;
    
    @JsonProperty("algorithm")
    private String algorithm;
    
    @JsonProperty("timestamp")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp;
    
    @JsonProperty("sessionId")
    private String sessionId;
    
    @JsonProperty("confidence")
    private Double confidence;

    /**
     * 推荐商品信息
     */
    public static class RecommendedProduct {
        @JsonProperty("productId")
        private String productId;
        
        @JsonProperty("score")
        private Double score;
        
        @JsonProperty("reason")
        private String reason;

        public RecommendedProduct() {}

        public RecommendedProduct(String productId, Double score, String reason) {
            this.productId = productId;
            this.score = score;
            this.reason = reason;
        }

        // Getters and Setters
        public String getProductId() {
            return productId;
        }

        public void setProductId(String productId) {
            this.productId = productId;
        }

        public Double getScore() {
            return score;
        }

        public void setScore(Double score) {
            this.score = score;
        }

        public String getReason() {
            return reason;
        }

        public void setReason(String reason) {
            this.reason = reason;
        }

        @Override
        public String toString() {
            return "RecommendedProduct{" +
                    "productId='" + productId + '\'' +
                    ", score=" + score +
                    ", reason='" + reason + '\'' +
                    '}';
        }
    }

    public Recommendation() {
        this.timestamp = LocalDateTime.now();
    }

    public Recommendation(String userId, List<RecommendedProduct> recommendedProducts, String algorithm) {
        this.userId = userId;
        this.recommendedProducts = recommendedProducts;
        this.algorithm = algorithm;
        this.timestamp = LocalDateTime.now();
    }

    // Getters and Setters
    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public List<RecommendedProduct> getRecommendedProducts() {
        return recommendedProducts;
    }

    public void setRecommendedProducts(List<RecommendedProduct> recommendedProducts) {
        this.recommendedProducts = recommendedProducts;
    }

    public String getAlgorithm() {
        return algorithm;
    }

    public void setAlgorithm(String algorithm) {
        this.algorithm = algorithm;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public Double getConfidence() {
        return confidence;
    }

    public void setConfidence(Double confidence) {
        this.confidence = confidence;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Recommendation that = (Recommendation) o;
        return Objects.equals(userId, that.userId) &&
                Objects.equals(timestamp, that.timestamp) &&
                Objects.equals(sessionId, that.sessionId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(userId, timestamp, sessionId);
    }

    @Override
    public String toString() {
        return "Recommendation{" +
                "userId='" + userId + '\'' +
                ", recommendedProducts=" + recommendedProducts +
                ", algorithm='" + algorithm + '\'' +
                ", timestamp=" + timestamp +
                ", sessionId='" + sessionId + '\'' +
                ", confidence=" + confidence +
                '}';
    }
}
