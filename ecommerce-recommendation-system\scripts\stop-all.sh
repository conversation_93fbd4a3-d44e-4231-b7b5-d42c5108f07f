#!/bin/bash

# 停止所有服务脚本

set -e

echo "=== 停止分布式实时电商推荐系统 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止消息源软件
stop_message_source() {
    log_info "停止消息源软件..."
    
    PID_FILE="logs/message-source.pid"
    
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p $PID > /dev/null 2>&1; then
            log_info "停止消息源软件 (PID: $PID)"
            kill $PID
            
            # 等待进程结束
            for i in {1..10}; do
                if ! ps -p $PID > /dev/null 2>&1; then
                    log_info "消息源软件已停止"
                    break
                fi
                sleep 1
            done
            
            # 如果进程仍在运行，强制杀死
            if ps -p $PID > /dev/null 2>&1; then
                log_warn "强制停止消息源软件"
                kill -9 $PID
            fi
        else
            log_warn "消息源软件进程不存在"
        fi
        
        rm -f "$PID_FILE"
    else
        log_warn "未找到消息源软件PID文件"
    fi
}

# 停止后端服务
stop_backend() {
    log_info "停止后端服务..."
    
    PID_FILE="logs/backend.pid"
    
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p $PID > /dev/null 2>&1; then
            log_info "停止后端服务 (PID: $PID)"
            kill $PID
            
            # 等待进程结束
            for i in {1..10}; do
                if ! ps -p $PID > /dev/null 2>&1; then
                    log_info "后端服务已停止"
                    break
                fi
                sleep 1
            done
            
            # 如果进程仍在运行，强制杀死
            if ps -p $PID > /dev/null 2>&1; then
                log_warn "强制停止后端服务"
                kill -9 $PID
            fi
        else
            log_warn "后端服务进程不存在"
        fi
        
        rm -f "$PID_FILE"
    else
        log_warn "未找到后端服务PID文件"
    fi
}

# 停止前端服务
stop_frontend() {
    log_info "停止前端服务..."
    
    PID_FILE="logs/frontend.pid"
    
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p $PID > /dev/null 2>&1; then
            log_info "停止前端服务 (PID: $PID)"
            kill $PID
            
            # 等待进程结束
            for i in {1..10}; do
                if ! ps -p $PID > /dev/null 2>&1; then
                    log_info "前端服务已停止"
                    break
                fi
                sleep 1
            done
            
            # 如果进程仍在运行，强制杀死
            if ps -p $PID > /dev/null 2>&1; then
                log_warn "强制停止前端服务"
                kill -9 $PID
            fi
        else
            log_warn "前端服务进程不存在"
        fi
        
        rm -f "$PID_FILE"
    else
        log_warn "未找到前端服务PID文件"
    fi
}

# 停止Flink作业
stop_flink_jobs() {
    log_info "停止Flink作业..."
    
    if docker ps | grep -q jobmanager; then
        # 获取所有运行中的作业
        RUNNING_JOBS=$(docker exec jobmanager flink list 2>/dev/null | grep "RUNNING" | awk '{print $4}' || true)
        
        if [ ! -z "$RUNNING_JOBS" ]; then
            for JOB_ID in $RUNNING_JOBS; do
                log_info "停止Flink作业: $JOB_ID"
                docker exec jobmanager flink cancel "$JOB_ID" || log_warn "停止作业失败: $JOB_ID"
            done
        else
            log_info "没有运行中的Flink作业"
        fi
    else
        log_warn "Flink JobManager未运行"
    fi
}

# 停止Docker容器
stop_docker_containers() {
    log_info "停止Docker容器..."
    
    if [ -f "docker-compose.yml" ]; then
        docker-compose down
        log_info "Docker容器已停止"
    else
        log_warn "未找到docker-compose.yml文件"
        
        # 手动停止容器
        CONTAINERS=$(docker ps -q --filter "name=kafka" --filter "name=zookeeper" --filter "name=redis" --filter "name=flink" 2>/dev/null || true)
        
        if [ ! -z "$CONTAINERS" ]; then
            log_info "手动停止相关容器..."
            docker stop $CONTAINERS
            docker rm $CONTAINERS
        fi
    fi
}

# 清理临时文件
cleanup_temp_files() {
    log_info "清理临时文件..."
    
    # 清理PID文件
    rm -f logs/*.pid
    
    # 清理临时日志文件
    find logs -name "*.log.*" -mtime +7 -delete 2>/dev/null || true
    
    log_info "临时文件清理完成"
}

# 显示停止后的状态
show_final_status() {
    log_info "=== 停止后状态 ==="
    
    # 检查进程
    echo "Java进程:"
    ps aux | grep java | grep -v grep || echo "  无Java进程运行"
    
    echo ""
    echo "Docker容器:"
    docker ps --format "table {{.Names}}\t{{.Status}}" | grep -E "(kafka|zookeeper|redis|flink)" || echo "  无相关容器运行"
    
    echo ""
    echo "端口占用:"
    netstat -tlnp 2>/dev/null | grep -E ":(8080|8081|3000|9092|9093|9094|2181|6379)" || echo "  无相关端口占用"
}

# 主函数
main() {
    log_info "开始停止所有服务..."
    
    # 停止应用程序
    stop_message_source
    stop_backend
    stop_frontend
    
    # 停止Flink作业
    stop_flink_jobs
    
    # 停止Docker容器
    stop_docker_containers
    
    # 清理临时文件
    cleanup_temp_files
    
    # 显示最终状态
    show_final_status
    
    log_info "=== 所有服务已停止 ==="
}

# 执行主函数
main "$@"
