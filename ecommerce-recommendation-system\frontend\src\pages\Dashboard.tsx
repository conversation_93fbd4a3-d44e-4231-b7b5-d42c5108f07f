import React, { useState, useEffect } from 'react';
import { <PERSON>, Row, Col, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Spin } from 'antd';
import {
  UserOutlined,
  ShoppingCartOutlined,
  HeartOutlined,
  TrophyOutlined
} from '@ant-design/icons';
import { <PERSON>Chart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { getSystemStatus } from '../services/api';

interface SystemStatus {
  timestamp: string;
  application: string;
  version: string;
  services: {
    backend: string;
    kafka: string;
    redis: string;
    flink: string;
  };
  jvm: {
    version: string;
    vendor: string;
    uptime: number;
    heapUsed: number;
    heapMax: number;
  };
}

const Dashboard: React.FC = () => {
  const [systemStatus, setSystemStatus] = useState<SystemStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 模拟数据
  const chartData = [
    { time: '00:00', users: 120, recommendations: 450 },
    { time: '04:00', users: 80, recommendations: 280 },
    { time: '08:00', users: 200, recommendations: 720 },
    { time: '12:00', users: 350, recommendations: 1200 },
    { time: '16:00', users: 280, recommendations: 980 },
    { time: '20:00', users: 400, recommendations: 1400 },
  ];

  useEffect(() => {
    fetchSystemStatus();
    const interval = setInterval(fetchSystemStatus, 30000); // 每30秒刷新
    return () => clearInterval(interval);
  }, []);

  const fetchSystemStatus = async () => {
    try {
      setLoading(true);
      const data = await getSystemStatus();
      setSystemStatus(data);
      setError(null);
    } catch (err) {
      setError('获取系统状态失败');
      console.error('Failed to fetch system status:', err);
    } finally {
      setLoading(false);
    }
  };

  const getServiceStatusColor = (status: string) => {
    switch (status) {
      case 'UP': return '#52c41a';
      case 'DOWN': return '#ff4d4f';
      case 'UNKNOWN': return '#faad14';
      default: return '#d9d9d9';
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatUptime = (uptime: number) => {
    const hours = Math.floor(uptime / (1000 * 60 * 60));
    const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}小时${minutes}分钟`;
  };

  if (loading && !systemStatus) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载系统状态中...</div>
      </div>
    );
  }

  return (
    <div>
      <h1>系统概览</h1>
      
      {error && (
        <Alert
          message="错误"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 系统状态卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="在线用户"
              value={1234}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="今日推荐"
              value={5678}
              prefix={<HeartOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="成功转化"
              value={890}
              prefix={<ShoppingCartOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="推荐准确率"
              value={85.6}
              suffix="%"
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 服务状态 */}
      {systemStatus && (
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={12}>
            <Card title="服务状态" loading={loading}>
              <Row gutter={16}>
                {Object.entries(systemStatus.services).map(([service, status]) => (
                  <Col span={12} key={service} style={{ marginBottom: 16 }}>
                    <Card size="small">
                      <Statistic
                        title={service.toUpperCase()}
                        value={status}
                        valueStyle={{ 
                          color: getServiceStatusColor(status),
                          fontSize: '16px'
                        }}
                      />
                    </Card>
                  </Col>
                ))}
              </Row>
            </Card>
          </Col>
          <Col span={12}>
            <Card title="JVM信息" loading={loading}>
              <div style={{ marginBottom: 8 }}>
                <strong>Java版本:</strong> {systemStatus.jvm.version}
              </div>
              <div style={{ marginBottom: 8 }}>
                <strong>运行时间:</strong> {formatUptime(systemStatus.jvm.uptime)}
              </div>
              <div style={{ marginBottom: 8 }}>
                <strong>堆内存使用:</strong> {formatBytes(systemStatus.jvm.heapUsed)} / {formatBytes(systemStatus.jvm.heapMax)}
              </div>
              <div>
                <strong>内存使用率:</strong> {((systemStatus.jvm.heapUsed / systemStatus.jvm.heapMax) * 100).toFixed(1)}%
              </div>
            </Card>
          </Col>
        </Row>
      )}

      {/* 实时数据图表 */}
      <Row gutter={16}>
        <Col span={24}>
          <Card title="实时数据趋势">
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="time" />
                <YAxis />
                <Tooltip />
                <Line 
                  type="monotone" 
                  dataKey="users" 
                  stroke="#8884d8" 
                  strokeWidth={2}
                  name="活跃用户"
                />
                <Line 
                  type="monotone" 
                  dataKey="recommendations" 
                  stroke="#82ca9d" 
                  strokeWidth={2}
                  name="推荐次数"
                />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
