package com.ecommerce.api.controller;

import com.ecommerce.api.service.SystemMonitorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 系统管理控制器
 */
@RestController
@RequestMapping("/api/system")
@Api(tags = "系统管理")
@CrossOrigin(origins = "*")
public class SystemController {

    @Autowired
    private SystemMonitorService systemMonitorService;

    @GetMapping("/status")
    @ApiOperation("获取系统状态")
    public ResponseEntity<Map<String, Object>> getSystemStatus() {
        Map<String, Object> status = systemMonitorService.getSystemStatus();
        return ResponseEntity.ok(status);
    }

    @GetMapping("/health")
    @ApiOperation("健康检查")
    public ResponseEntity<Map<String, String>> healthCheck() {
        Map<String, String> health = Map.of(
            "status", "UP",
            "timestamp", java.time.LocalDateTime.now().toString()
        );
        return ResponseEntity.ok(health);
    }

    @GetMapping("/metrics")
    @ApiOperation("获取系统指标")
    public ResponseEntity<Map<String, Object>> getMetrics() {
        Map<String, Object> metrics = systemMonitorService.getMetrics();
        return ResponseEntity.ok(metrics);
    }

    @PostMapping("/kafka/topics/create")
    @ApiOperation("创建Kafka主题")
    public ResponseEntity<Map<String, String>> createKafkaTopic(
            @RequestParam String topicName,
            @RequestParam(defaultValue = "3") int partitions,
            @RequestParam(defaultValue = "3") short replicationFactor) {
        
        try {
            systemMonitorService.createKafkaTopic(topicName, partitions, replicationFactor);
            return ResponseEntity.ok(Map.of(
                "status", "success",
                "message", "主题创建成功: " + topicName
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "status", "error",
                "message", "主题创建失败: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/kafka/topics")
    @ApiOperation("获取Kafka主题列表")
    public ResponseEntity<Map<String, Object>> getKafkaTopics() {
        try {
            Map<String, Object> topics = systemMonitorService.getKafkaTopics();
            return ResponseEntity.ok(topics);
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(Map.of(
                "status", "error",
                "message", "获取主题列表失败: " + e.getMessage()
            ));
        }
    }
}
