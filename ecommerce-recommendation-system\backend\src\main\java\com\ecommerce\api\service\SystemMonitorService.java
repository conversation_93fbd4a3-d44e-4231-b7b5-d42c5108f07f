package com.ecommerce.api.service;

import org.apache.kafka.clients.admin.*;
import org.apache.kafka.common.KafkaFuture;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.RuntimeMXBean;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutionException;

/**
 * 系统监控服务
 */
@Service
public class SystemMonitorService {

    private static final Logger logger = LoggerFactory.getLogger(SystemMonitorService.class);

    @Value("${spring.kafka.bootstrap-servers}")
    private String kafkaBootstrapServers;

    private final RedisTemplate<String, Object> redisTemplate;
    private AdminClient kafkaAdminClient;

    public SystemMonitorService(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @PostConstruct
    public void init() {
        // 初始化Kafka管理客户端
        Properties props = new Properties();
        props.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaBootstrapServers);
        kafkaAdminClient = AdminClient.create(props);
    }

    @PreDestroy
    public void destroy() {
        if (kafkaAdminClient != null) {
            kafkaAdminClient.close();
        }
    }

    /**
     * 获取系统状态
     */
    public Map<String, Object> getSystemStatus() {
        Map<String, Object> status = new HashMap<>();
        
        // 基本信息
        status.put("timestamp", LocalDateTime.now());
        status.put("application", "E-commerce Recommendation System");
        status.put("version", "1.0.0");
        
        // 服务状态
        Map<String, String> services = new HashMap<>();
        services.put("backend", "UP");
        services.put("kafka", checkKafkaStatus());
        services.put("redis", checkRedisStatus());
        services.put("flink", checkFlinkStatus());
        status.put("services", services);
        
        // JVM信息
        status.put("jvm", getJvmInfo());
        
        return status;
    }

    /**
     * 获取系统指标
     */
    public Map<String, Object> getMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        
        // JVM指标
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        RuntimeMXBean runtimeBean = ManagementFactory.getRuntimeMXBean();
        
        Map<String, Object> jvmMetrics = new HashMap<>();
        jvmMetrics.put("heapUsed", memoryBean.getHeapMemoryUsage().getUsed());
        jvmMetrics.put("heapMax", memoryBean.getHeapMemoryUsage().getMax());
        jvmMetrics.put("nonHeapUsed", memoryBean.getNonHeapMemoryUsage().getUsed());
        jvmMetrics.put("uptime", runtimeBean.getUptime());
        
        metrics.put("jvm", jvmMetrics);
        
        // Kafka指标
        try {
            metrics.put("kafka", getKafkaMetrics());
        } catch (Exception e) {
            logger.error("Failed to get Kafka metrics", e);
            metrics.put("kafka", Map.of("error", e.getMessage()));
        }
        
        return metrics;
    }

    /**
     * 创建Kafka主题
     */
    public void createKafkaTopic(String topicName, int partitions, short replicationFactor) 
            throws ExecutionException, InterruptedException {
        
        NewTopic newTopic = new NewTopic(topicName, partitions, replicationFactor);
        CreateTopicsResult result = kafkaAdminClient.createTopics(Collections.singletonList(newTopic));
        result.all().get(); // 等待完成
        
        logger.info("Created Kafka topic: {} with {} partitions and replication factor {}", 
                   topicName, partitions, replicationFactor);
    }

    /**
     * 获取Kafka主题列表
     */
    public Map<String, Object> getKafkaTopics() throws ExecutionException, InterruptedException {
        ListTopicsResult listTopicsResult = kafkaAdminClient.listTopics();
        Set<String> topicNames = listTopicsResult.names().get();
        
        Map<String, Object> result = new HashMap<>();
        result.put("topics", topicNames);
        result.put("count", topicNames.size());
        
        return result;
    }

    private String checkKafkaStatus() {
        try {
            ListTopicsResult result = kafkaAdminClient.listTopics();
            result.names().get(); // 测试连接
            return "UP";
        } catch (Exception e) {
            logger.warn("Kafka health check failed", e);
            return "DOWN";
        }
    }

    private String checkRedisStatus() {
        try {
            redisTemplate.opsForValue().set("health:check", "ok");
            String value = (String) redisTemplate.opsForValue().get("health:check");
            return "ok".equals(value) ? "UP" : "DOWN";
        } catch (Exception e) {
            logger.warn("Redis health check failed", e);
            return "DOWN";
        }
    }

    private String checkFlinkStatus() {
        // 简单的Flink状态检查（可以通过HTTP API调用Flink）
        try {
            // 这里可以添加对Flink REST API的调用
            // 暂时返回UNKNOWN
            return "UNKNOWN";
        } catch (Exception e) {
            logger.warn("Flink health check failed", e);
            return "DOWN";
        }
    }

    private Map<String, Object> getJvmInfo() {
        RuntimeMXBean runtimeBean = ManagementFactory.getRuntimeMXBean();
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        
        Map<String, Object> jvmInfo = new HashMap<>();
        jvmInfo.put("version", System.getProperty("java.version"));
        jvmInfo.put("vendor", System.getProperty("java.vendor"));
        jvmInfo.put("uptime", runtimeBean.getUptime());
        jvmInfo.put("heapUsed", memoryBean.getHeapMemoryUsage().getUsed());
        jvmInfo.put("heapMax", memoryBean.getHeapMemoryUsage().getMax());
        
        return jvmInfo;
    }

    private Map<String, Object> getKafkaMetrics() throws ExecutionException, InterruptedException {
        Map<String, Object> kafkaMetrics = new HashMap<>();
        
        // 获取主题数量
        ListTopicsResult topicsResult = kafkaAdminClient.listTopics();
        Set<String> topics = topicsResult.names().get();
        kafkaMetrics.put("topicCount", topics.size());
        
        // 获取集群信息
        DescribeClusterResult clusterResult = kafkaAdminClient.describeCluster();
        kafkaMetrics.put("nodeCount", clusterResult.nodes().get().size());
        kafkaMetrics.put("clusterId", clusterResult.clusterId().get());
        
        return kafkaMetrics;
    }
}
