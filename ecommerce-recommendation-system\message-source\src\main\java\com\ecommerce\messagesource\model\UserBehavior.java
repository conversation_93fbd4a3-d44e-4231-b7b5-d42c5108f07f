package com.ecommerce.messagesource.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 用户行为实体类
 */
public class UserBehavior {
    
    @JsonProperty("userId")
    private String userId;
    
    @JsonProperty("productId")
    private String productId;
    
    @JsonProperty("behaviorType")
    private BehaviorType behaviorType;
    
    @JsonProperty("timestamp")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp;
    
    @JsonProperty("sessionId")
    private String sessionId;
    
    @JsonProperty("quantity")
    private Integer quantity;
    
    @JsonProperty("price")
    private Double price;

    public enum BehaviorType {
        VIEW("浏览"),
        CLICK("点击"),
        ADD_TO_CART("加入购物车"),
        PURCHASE("购买"),
        FAVORITE("收藏"),
        SHARE("分享");

        private final String description;

        BehaviorType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    public UserBehavior() {
        this.timestamp = LocalDateTime.now();
    }

    public UserBehavior(String userId, String productId, BehaviorType behaviorType) {
        this.userId = userId;
        this.productId = productId;
        this.behaviorType = behaviorType;
        this.timestamp = LocalDateTime.now();
    }

    public UserBehavior(String userId, String productId, BehaviorType behaviorType, 
                       String sessionId, Integer quantity, Double price) {
        this.userId = userId;
        this.productId = productId;
        this.behaviorType = behaviorType;
        this.sessionId = sessionId;
        this.quantity = quantity;
        this.price = price;
        this.timestamp = LocalDateTime.now();
    }

    // Getters and Setters
    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public BehaviorType getBehaviorType() {
        return behaviorType;
    }

    public void setBehaviorType(BehaviorType behaviorType) {
        this.behaviorType = behaviorType;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserBehavior that = (UserBehavior) o;
        return Objects.equals(userId, that.userId) &&
                Objects.equals(productId, that.productId) &&
                behaviorType == that.behaviorType &&
                Objects.equals(timestamp, that.timestamp);
    }

    @Override
    public int hashCode() {
        return Objects.hash(userId, productId, behaviorType, timestamp);
    }

    @Override
    public String toString() {
        return "UserBehavior{" +
                "userId='" + userId + '\'' +
                ", productId='" + productId + '\'' +
                ", behaviorType=" + behaviorType +
                ", timestamp=" + timestamp +
                ", sessionId='" + sessionId + '\'' +
                ", quantity=" + quantity +
                ", price=" + price +
                '}';
    }
}
