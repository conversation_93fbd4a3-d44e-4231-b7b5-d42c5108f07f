version: '3.8'

services:
  # ZooKeeper服务 - 使用轻量级镜像
  zookeeper:
    image: wurstmeister/zookeeper:latest
    hostname: zookeeper
    container_name: zookeeper
    ports:
      - "2181:2181"
    volumes:
      - zookeeper-data:/opt/zookeeper-3.4.13/data
    restart: unless-stopped

  # Kafka服务 - 使用wurstmeister镜像（更容易下载）
  kafka1:
    image: wurstmeister/kafka:latest
    hostname: kafka1
    container_name: kafka1
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_ADVERTISED_HOST_NAME: localhost
      KAFKA_ADVERTISED_PORT: 9092
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_BROKER_ID: 1
      KAFKA_CREATE_TOPICS: "user-behavior:3:1,product-events:3:1,recommendations:3:1"
    volumes:
      - kafka1-data:/kafka
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:alpine
    hostname: redis
    container_name: redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped

  # Flink JobManager - 使用官方轻量镜像
  jobmanager:
    image: flink:1.17-scala_2.12
    hostname: jobmanager
    container_name: jobmanager
    ports:
      - "8081:8081"
    command: jobmanager
    environment:
      - |
        FLINK_PROPERTIES=
        jobmanager.rpc.address: jobmanager
        parallelism.default: 2
    volumes:
      - flink-data:/opt/flink/data
    restart: unless-stopped

  # Flink TaskManager
  taskmanager:
    image: flink:1.17-scala_2.12
    hostname: taskmanager
    container_name: taskmanager
    depends_on:
      - jobmanager
    command: taskmanager
    environment:
      - |
        FLINK_PROPERTIES=
        jobmanager.rpc.address: jobmanager
        taskmanager.numberOfTaskSlots: 4
        parallelism.default: 2
    volumes:
      - flink-data:/opt/flink/data
    restart: unless-stopped

volumes:
  zookeeper-data:
  kafka1-data:
  redis-data:
  flink-data:

networks:
  default:
    name: ecommerce-network
