package com.ecommerce.messagesource.service;

import com.ecommerce.messagesource.kafka.KafkaProducerService;
import com.ecommerce.messagesource.model.Product;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 商品服务
 */
public class ProductService {
    
    private static final Logger logger = LoggerFactory.getLogger(ProductService.class);
    
    private final KafkaProducerService kafkaProducer;
    private final Map<String, Product> productStore = new ConcurrentHashMap<>();
    private final Random random = new Random();
    
    // 预定义的商品数据
    private static final String[] CATEGORIES = {
        "电子产品", "服装", "家居用品", "图书", "运动户外", "美妆护肤", "食品饮料", "汽车用品"
    };
    
    private static final String[] BRANDS = {
        "Apple", "Samsung", "Nike", "Adidas", "Sony", "LG", "华为", "小米", 
        "ZARA", "H&M", "IKEA", "无印良品", "可口可乐", "百事可乐"
    };
    
    private static final String[] PRODUCT_NAMES = {
        "智能手机", "笔记本电脑", "运动鞋", "T恤", "咖啡杯", "台灯", "耳机", "充电器",
        "背包", "手表", "键盘", "鼠标", "音响", "相机", "平板电脑", "游戏机"
    };
    
    public ProductService(KafkaProducerService kafkaProducer) {
        this.kafkaProducer = kafkaProducer;
        initializeProducts();
    }
    
    /**
     * 初始化一些预设商品
     */
    private void initializeProducts() {
        for (int i = 1; i <= 50; i++) {
            Product product = generateRandomProduct("PROD_" + String.format("%03d", i));
            productStore.put(product.getProductId(), product);
        }
        logger.info("Initialized {} products", productStore.size());
    }
    
    /**
     * 创建新商品
     */
    public Product createProduct(String productName, String category, Double price, 
                               String description, String brand, Integer stock) {
        String productId = "PROD_" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
        
        Product product = new Product(productId, productName, category, price, 
                                    description, brand, stock);
        
        productStore.put(productId, product);
        
        // 发送商品创建消息到Kafka
        try {
            kafkaProducer.sendMessage("product-events", productId, product);
            logger.info("Created and sent product to Kafka: {}", product);
        } catch (Exception e) {
            logger.error("Failed to send product to Kafka: {}", productId, e);
        }
        
        return product;
    }
    
    /**
     * 生成随机商品
     */
    public Product createRandomProduct() {
        String productId = "PROD_" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
        return createProduct(
            getRandomProductName(),
            getRandomCategory(),
            generateRandomPrice(),
            "这是一个优质的商品，值得购买！",
            getRandomBrand(),
            random.nextInt(1000) + 10
        );
    }
    
    /**
     * 生成随机商品（内部使用）
     */
    private Product generateRandomProduct(String productId) {
        return new Product(
            productId,
            getRandomProductName(),
            getRandomCategory(),
            generateRandomPrice(),
            "这是一个优质的商品，值得购买！",
            getRandomBrand(),
            random.nextInt(1000) + 10
        );
    }
    
    /**
     * 获取商品信息
     */
    public Product getProduct(String productId) {
        return productStore.get(productId);
    }
    
    /**
     * 获取所有商品
     */
    public List<Product> getAllProducts() {
        return new ArrayList<>(productStore.values());
    }
    
    /**
     * 根据分类获取商品
     */
    public List<Product> getProductsByCategory(String category) {
        return productStore.values().stream()
                .filter(product -> category.equals(product.getCategory()))
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }
    
    /**
     * 获取随机商品
     */
    public Product getRandomProduct() {
        List<Product> products = getAllProducts();
        if (products.isEmpty()) {
            return null;
        }
        return products.get(random.nextInt(products.size()));
    }
    
    /**
     * 获取多个随机商品
     */
    public List<Product> getRandomProducts(int count) {
        List<Product> allProducts = getAllProducts();
        if (allProducts.size() <= count) {
            return new ArrayList<>(allProducts);
        }
        
        Collections.shuffle(allProducts);
        return allProducts.subList(0, count);
    }
    
    // 辅助方法
    private String getRandomProductName() {
        return PRODUCT_NAMES[random.nextInt(PRODUCT_NAMES.length)];
    }
    
    private String getRandomCategory() {
        return CATEGORIES[random.nextInt(CATEGORIES.length)];
    }
    
    private String getRandomBrand() {
        return BRANDS[random.nextInt(BRANDS.length)];
    }
    
    private Double generateRandomPrice() {
        return Math.round((random.nextDouble() * 9900 + 100) * 100.0) / 100.0; // 100-10000元
    }
    
    /**
     * 获取商品总数
     */
    public int getProductCount() {
        return productStore.size();
    }
}
