import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Modal, Form, Input, InputNumber, message, Space, Tag } from 'antd';
import { PlusOutlined, ReloadOutlined } from '@ant-design/icons';
import { getKafkaTopics, createKafkaTopic } from '../services/api';

interface Topic {
  name: string;
  partitions?: number;
  replicationFactor?: number;
}

const KafkaManagement: React.FC = () => {
  const [topics, setTopics] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchTopics();
  }, []);

  const fetchTopics = async () => {
    try {
      setLoading(true);
      const data = await getKafkaTopics();
      setTopics(Array.from(data.topics));
    } catch (error) {
      message.error('获取主题列表失败');
      console.error('Failed to fetch topics:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTopic = async (values: any) => {
    try {
      await createKafkaTopic(values.topicName, values.partitions, values.replicationFactor);
      message.success('主题创建成功');
      setModalVisible(false);
      form.resetFields();
      fetchTopics();
    } catch (error) {
      message.error('主题创建失败');
      console.error('Failed to create topic:', error);
    }
  };

  const getTopicType = (topicName: string) => {
    if (topicName.includes('user-behavior')) {
      return <Tag color="blue">用户行为</Tag>;
    } else if (topicName.includes('product')) {
      return <Tag color="green">商品事件</Tag>;
    } else if (topicName.includes('recommendation')) {
      return <Tag color="purple">推荐结果</Tag>;
    } else {
      return <Tag color="default">其他</Tag>;
    }
  };

  const columns = [
    {
      title: '主题名称',
      dataIndex: 'name',
      key: 'name',
      render: (name: string) => <strong>{name}</strong>,
    },
    {
      title: '类型',
      dataIndex: 'name',
      key: 'type',
      render: (name: string) => getTopicType(name),
    },
    {
      title: '分区数',
      dataIndex: 'partitions',
      key: 'partitions',
      render: () => '3', // 默认值，实际应该从Kafka获取
    },
    {
      title: '副本因子',
      dataIndex: 'replicationFactor',
      key: 'replicationFactor',
      render: () => '3', // 默认值，实际应该从Kafka获取
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: any) => (
        <Space size="middle">
          <Button type="link" size="small">
            查看详情
          </Button>
          <Button type="link" size="small" danger>
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const tableData = topics.map(topic => ({
    key: topic,
    name: topic,
  }));

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <h1>Kafka主题管理</h1>
        <Space>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={fetchTopics}
            loading={loading}
          >
            刷新
          </Button>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => setModalVisible(true)}
          >
            创建主题
          </Button>
        </Space>
      </div>

      <Card>
        <Table
          columns={columns}
          dataSource={tableData}
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个主题`,
          }}
        />
      </Card>

      <Modal
        title="创建Kafka主题"
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
        }}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateTopic}
          initialValues={{
            partitions: 3,
            replicationFactor: 3,
          }}
        >
          <Form.Item
            label="主题名称"
            name="topicName"
            rules={[
              { required: true, message: '请输入主题名称' },
              { pattern: /^[a-zA-Z0-9._-]+$/, message: '主题名称只能包含字母、数字、点、下划线和连字符' },
            ]}
          >
            <Input placeholder="请输入主题名称" />
          </Form.Item>

          <Form.Item
            label="分区数"
            name="partitions"
            rules={[{ required: true, message: '请输入分区数' }]}
          >
            <InputNumber min={1} max={100} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            label="副本因子"
            name="replicationFactor"
            rules={[{ required: true, message: '请输入副本因子' }]}
          >
            <InputNumber min={1} max={10} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setModalVisible(false);
                form.resetFields();
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                创建
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default KafkaManagement;
