import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Table, Tag, Button, Alert, Spin, Progress } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import { getSystemStatus, getMetrics } from '../services/api';

interface SystemMetrics {
  jvm: {
    heapUsed: number;
    heapMax: number;
    nonHeapUsed: number;
    uptime: number;
  };
  kafka: {
    topicCount: number;
    nodeCount: number;
    clusterId: string;
  };
}

const SystemStatus: React.FC = () => {
  const [systemStatus, setSystemStatus] = useState<any>(null);
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [statusData, metricsData] = await Promise.all([
        getSystemStatus(),
        getMetrics()
      ]);
      
      setSystemStatus(statusData);
      setMetrics(metricsData);
    } catch (err) {
      setError('获取系统数据失败');
      console.error('Failed to fetch system data:', err);
    } finally {
      setLoading(false);
    }
  };

  const getStatusTag = (status: string) => {
    const statusConfig = {
      'UP': { color: 'green', text: '正常' },
      'DOWN': { color: 'red', text: '异常' },
      'UNKNOWN': { color: 'orange', text: '未知' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatUptime = (uptime: number) => {
    const days = Math.floor(uptime / (1000 * 60 * 60 * 24));
    const hours = Math.floor((uptime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));
    
    if (days > 0) {
      return `${days}天${hours}小时${minutes}分钟`;
    } else if (hours > 0) {
      return `${hours}小时${minutes}分钟`;
    } else {
      return `${minutes}分钟`;
    }
  };

  const serviceColumns = [
    {
      title: '服务名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
  ];

  const getServiceData = () => {
    if (!systemStatus?.services) return [];
    
    const serviceDescriptions = {
      backend: '后端API服务',
      kafka: 'Kafka消息队列',
      redis: 'Redis缓存服务',
      flink: 'Flink流处理引擎'
    };
    
    return Object.entries(systemStatus.services).map(([name, status]) => ({
      key: name,
      name: name.toUpperCase(),
      status,
      description: serviceDescriptions[name as keyof typeof serviceDescriptions] || '未知服务'
    }));
  };

  if (loading && !systemStatus) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载系统状态中...</div>
      </div>
    );
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <h1>系统状态</h1>
        <Button 
          type="primary" 
          icon={<ReloadOutlined />} 
          onClick={fetchData}
          loading={loading}
        >
          刷新
        </Button>
      </div>

      {error && (
        <Alert
          message="错误"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 基本信息 */}
      {systemStatus && (
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={12}>
            <Card title="系统信息">
              <div style={{ marginBottom: 8 }}>
                <strong>应用名称:</strong> {systemStatus.application}
              </div>
              <div style={{ marginBottom: 8 }}>
                <strong>版本:</strong> {systemStatus.version}
              </div>
              <div>
                <strong>更新时间:</strong> {new Date(systemStatus.timestamp).toLocaleString()}
              </div>
            </Card>
          </Col>
          <Col span={12}>
            <Card title="JVM信息">
              <div style={{ marginBottom: 8 }}>
                <strong>Java版本:</strong> {systemStatus.jvm.version}
              </div>
              <div style={{ marginBottom: 8 }}>
                <strong>供应商:</strong> {systemStatus.jvm.vendor}
              </div>
              <div>
                <strong>运行时间:</strong> {formatUptime(systemStatus.jvm.uptime)}
              </div>
            </Card>
          </Col>
        </Row>
      )}

      {/* 内存使用情况 */}
      {metrics && (
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={12}>
            <Card title="内存使用情况">
              <div style={{ marginBottom: 16 }}>
                <div style={{ marginBottom: 8 }}>
                  <strong>堆内存:</strong> {formatBytes(metrics.jvm.heapUsed)} / {formatBytes(metrics.jvm.heapMax)}
                </div>
                <Progress 
                  percent={Math.round((metrics.jvm.heapUsed / metrics.jvm.heapMax) * 100)}
                  status={metrics.jvm.heapUsed / metrics.jvm.heapMax > 0.8 ? 'exception' : 'normal'}
                />
              </div>
              <div>
                <div style={{ marginBottom: 8 }}>
                  <strong>非堆内存:</strong> {formatBytes(metrics.jvm.nonHeapUsed)}
                </div>
              </div>
            </Card>
          </Col>
          <Col span={12}>
            <Card title="Kafka集群信息">
              <div style={{ marginBottom: 8 }}>
                <strong>集群ID:</strong> {metrics.kafka.clusterId}
              </div>
              <div style={{ marginBottom: 8 }}>
                <strong>节点数量:</strong> {metrics.kafka.nodeCount}
              </div>
              <div>
                <strong>主题数量:</strong> {metrics.kafka.topicCount}
              </div>
            </Card>
          </Col>
        </Row>
      )}

      {/* 服务状态表格 */}
      <Row gutter={16}>
        <Col span={24}>
          <Card title="服务状态详情">
            <Table
              columns={serviceColumns}
              dataSource={getServiceData()}
              pagination={false}
              loading={loading}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default SystemStatus;
