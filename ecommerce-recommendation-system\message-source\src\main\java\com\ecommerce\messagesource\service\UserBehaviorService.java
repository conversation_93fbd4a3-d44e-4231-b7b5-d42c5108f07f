package com.ecommerce.messagesource.service;

import com.ecommerce.messagesource.kafka.KafkaProducerService;
import com.ecommerce.messagesource.model.Product;
import com.ecommerce.messagesource.model.UserBehavior;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Random;
import java.util.UUID;

/**
 * 用户行为服务
 */
public class UserBehaviorService {
    
    private static final Logger logger = LoggerFactory.getLogger(UserBehaviorService.class);
    
    private final KafkaProducerService kafkaProducer;
    private final ProductService productService;
    private final Random random = new Random();
    
    // 预定义的用户ID
    private static final String[] USER_IDS = {
        "user001", "user002", "user003", "user004", "user005",
        "user006", "user007", "user008", "user009", "user010",
        "user011", "user012", "user013", "user014", "user015",
        "user016", "user017", "user018", "user019", "user020"
    };
    
    public UserBehaviorService(KafkaProducerService kafkaProducer, ProductService productService) {
        this.kafkaProducer = kafkaProducer;
        this.productService = productService;
    }
    
    /**
     * 模拟用户购买行为
     */
    public UserBehavior simulatePurchase(String userId, String productId) {
        Product product = productService.getProduct(productId);
        if (product == null) {
            logger.warn("Product not found: {}", productId);
            return null;
        }
        
        String sessionId = generateSessionId();
        int quantity = random.nextInt(3) + 1; // 1-3个
        
        UserBehavior behavior = new UserBehavior(
            userId, 
            productId, 
            UserBehavior.BehaviorType.PURCHASE,
            sessionId,
            quantity,
            product.getPrice() * quantity
        );
        
        // 发送用户行为消息到Kafka
        try {
            kafkaProducer.sendMessage("user-behavior", userId, behavior);
            logger.info("Simulated purchase behavior: User {} bought {} x{} for ${}", 
                       userId, productId, quantity, behavior.getPrice());
        } catch (Exception e) {
            logger.error("Failed to send user behavior to Kafka: {}", behavior, e);
        }
        
        return behavior;
    }
    
    /**
     * 模拟随机用户购买行为
     */
    public UserBehavior simulateRandomPurchase() {
        String userId = getRandomUserId();
        Product product = productService.getRandomProduct();
        
        if (product == null) {
            logger.warn("No products available for random purchase");
            return null;
        }
        
        return simulatePurchase(userId, product.getProductId());
    }
    
    /**
     * 模拟用户浏览行为
     */
    public UserBehavior simulateView(String userId, String productId) {
        String sessionId = generateSessionId();
        
        UserBehavior behavior = new UserBehavior(userId, productId, UserBehavior.BehaviorType.VIEW);
        behavior.setSessionId(sessionId);
        
        // 发送用户行为消息到Kafka
        try {
            kafkaProducer.sendMessage("user-behavior", userId, behavior);
            logger.info("Simulated view behavior: User {} viewed {}", userId, productId);
        } catch (Exception e) {
            logger.error("Failed to send user behavior to Kafka: {}", behavior, e);
        }
        
        return behavior;
    }
    
    /**
     * 模拟用户点击行为
     */
    public UserBehavior simulateClick(String userId, String productId) {
        String sessionId = generateSessionId();
        
        UserBehavior behavior = new UserBehavior(userId, productId, UserBehavior.BehaviorType.CLICK);
        behavior.setSessionId(sessionId);
        
        // 发送用户行为消息到Kafka
        try {
            kafkaProducer.sendMessage("user-behavior", userId, behavior);
            logger.info("Simulated click behavior: User {} clicked {}", userId, productId);
        } catch (Exception e) {
            logger.error("Failed to send user behavior to Kafka: {}", behavior, e);
        }
        
        return behavior;
    }
    
    /**
     * 模拟用户加入购物车行为
     */
    public UserBehavior simulateAddToCart(String userId, String productId) {
        Product product = productService.getProduct(productId);
        if (product == null) {
            logger.warn("Product not found: {}", productId);
            return null;
        }
        
        String sessionId = generateSessionId();
        int quantity = random.nextInt(2) + 1; // 1-2个
        
        UserBehavior behavior = new UserBehavior(
            userId, 
            productId, 
            UserBehavior.BehaviorType.ADD_TO_CART,
            sessionId,
            quantity,
            product.getPrice() * quantity
        );
        
        // 发送用户行为消息到Kafka
        try {
            kafkaProducer.sendMessage("user-behavior", userId, behavior);
            logger.info("Simulated add to cart behavior: User {} added {} x{} to cart", 
                       userId, productId, quantity);
        } catch (Exception e) {
            logger.error("Failed to send user behavior to Kafka: {}", behavior, e);
        }
        
        return behavior;
    }
    
    /**
     * 模拟随机用户行为
     */
    public UserBehavior simulateRandomBehavior() {
        String userId = getRandomUserId();
        Product product = productService.getRandomProduct();
        
        if (product == null) {
            logger.warn("No products available for random behavior");
            return null;
        }
        
        UserBehavior.BehaviorType[] behaviorTypes = UserBehavior.BehaviorType.values();
        UserBehavior.BehaviorType randomType = behaviorTypes[random.nextInt(behaviorTypes.length)];
        
        switch (randomType) {
            case VIEW:
                return simulateView(userId, product.getProductId());
            case CLICK:
                return simulateClick(userId, product.getProductId());
            case ADD_TO_CART:
                return simulateAddToCart(userId, product.getProductId());
            case PURCHASE:
                return simulatePurchase(userId, product.getProductId());
            default:
                return simulateView(userId, product.getProductId());
        }
    }
    
    /**
     * 批量模拟用户行为
     */
    public void simulateBatchBehaviors(int count) {
        logger.info("Starting to simulate {} user behaviors", count);
        
        for (int i = 0; i < count; i++) {
            try {
                simulateRandomBehavior();
                Thread.sleep(100); // 间隔100ms
            } catch (InterruptedException e) {
                logger.warn("Batch simulation interrupted", e);
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                logger.error("Error in batch simulation at iteration {}", i, e);
            }
        }
        
        logger.info("Completed simulating {} user behaviors", count);
    }
    
    // 辅助方法
    private String getRandomUserId() {
        return USER_IDS[random.nextInt(USER_IDS.length)];
    }
    
    private String generateSessionId() {
        return "session_" + UUID.randomUUID().toString().substring(0, 8);
    }
}
