第一项：分布式实时电商推荐系统
在当今的数字化时代，电子商务已成为人们日常生活的重要组成部分。随
着电商平台的日益增多，用户面临着海量的商品选择，如何在众多商品中快速
找到符合自己需求和喜好的产品，成为了用户的一大痛点。同时，电商平台也
面临着激烈的竞争，如何通过提升用户体验来增加用户粘性，提高转化率和销
售额，成为了电商企业亟需解决的问题。
传统的推荐系统大多基于用户的历史行为数据进行离线分析，生成推荐结
果。然而，这种方法存在明显的延迟，无法及时反映用户的最新兴趣和需求。
此外，随着大数据和人工智能技术的快速发展，实时数据处理和个性化推荐成
为了可能。
Flink和 Kafka作为大数据处理领域的佼佼者，分别以其强大的实时数据处
理能力和高可靠性的消息队列服务，成为了构建实时推荐系统的理想选择。
基于上述背景，本项目旨在模拟构建一个实时、个性化、可扩展、稳定且
可解释的电商推荐系统，为用户提供更好的购物体验，同时为电商企业创造更
大的商业价值。
一、具体实验内容：
1. 消息源软件开发
 构建推荐系统数据源工程，模拟用户的购买、操作行为。
 获取推荐结果，并将推荐结果打印到终端。
2. 数据生产和消费（Kafka）
 配置 Kafka集群，创建用于传递用户行为数据和推荐结果的 Kafka主
题。
 开发 Kafka生产者，模拟用户行为数据的生成，并将其发送到 Kafka
主题中。
 开发 Kafka消费者，从 Kafka主题中消费用户行为数据，并将其传递
给 Flink进行处理。
 开发另一个 Kafka消费者，从 Flink处理后的 Kafka主题中消费推荐
结果，并将其传递给消息源软件。
3. 数据处理和推荐（Flink）
 配置 Flink集群，编写 Flink作业，用于处理从 Kafka接收到的用户行
为数据。
 实现简单的推荐算法，根据用户行为数据计算推荐结果。
 将推荐结果发送到另一个 Kafka主题中，供业务系统使用。
4. 系统集成和测试
 将消息源软件、Kafka和 Flink等组件进行集成，确保各组件之间的数
据传递和交互正常。
 进行功能测试，验证推荐系统的正确性、实时性和稳定性。
 进行性能测试，评估推荐系统的处理能力和响应时间。
二、数据及相关工具
1. 数据集
本项目数据不依赖数据集，而是通过实验者在系统上模拟购买、点击等操
作。这些模拟数据用于测试实时系统的功能和性能，而不是用于实际业务决策。
实验者可以模拟高并发访问、数据异常等情况，观察系统的响应和处理能力，
确保系统在实际应用中能够稳定运行。
2. 运行环境
1）操作系统要求
节点规模：3节点
节点名称 Hadoop01 Hadoop02 Hadoop03
部署服务 JobManager
TaskManager
Kafka Broker
ZooKeeper
TaskManager
Kafka Broker
TaskManager
Kafka Broker
操作系统: 使用Linux（Ubuntu 20.04 或CentOS 7）
硬件要求:
CPU: 至少2 核CPU，推荐4 核以上。
内存: 最少8 GB RAM，推荐16 GB 及以上。
存储: 至少50 GB 可用空间，具体取决于数据量和日志存储需求。
网络: 多节点配置需要稳定的网络连接，保证Kafka 和Flink 的集群部署。
2）软件要求
消息队列: Apache Kafka: 3.6.0
数据流处理引擎: Apache Flink 1.17
3）Kafka 环境要求
Zookeeper：
Kafka 使用Zookeeper 进行集群管理和协调，至少配置一个Zookeeper 节点。
Kafka Broker：
至少3 个Kafka Broker 节点以保证数据的高可用性和容错性。
4）Flink 环境要求
至少需要1 个JobManager 和3 个TaskManager 节点来进行流处理计算，
配置Flink 和Kafka 的连接器（如FlinkKafkaConsumer 和FlinkKafkaProducer）
用于处理消息流。
5）整体要求
 通过消息源软件，可以创建一个新的商品
 消息源软件针对商品信息，生产一条新的 Kafka 消息
 Flink 可以消费 Kafka 生产的消息，并在推荐模型中记录商品信息
 通过消息源软件，生成一个新的用户名、目标商品，模拟购买操作
 消息源软件针对购买操作，产生 Kafka 消息
 Flink 消费消息源软件生产的 Kafka 消息，并生产推荐结果，生产一条
Kafka 消息
 消息源软件收到 Flink 生产的推荐结果 Kafka 消息
1）分布式 Kafka 环境配置（
完成 Kafka环境，实现消息订阅、发布、Topic创建等功能。
2）分布式 Flink 环境配置
完成 Flink环境搭建，实现从 Kafka处消费数据、进行实时数据处理、并
将结果发布到 Kafka。
3）实现推荐系统工程
完成推荐系统工程的创建，在 Flink中现任意的推荐算法。
4）实现消息源软件工程
完成消息源软件，实现整体的业务流程，模拟完整的分布式实时推荐系统
