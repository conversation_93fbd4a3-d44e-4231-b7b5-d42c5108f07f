package com.ecommerce.messagesource;

import com.ecommerce.messagesource.kafka.KafkaConsumerService;
import com.ecommerce.messagesource.kafka.KafkaProducerService;
import com.ecommerce.messagesource.model.Product;
import com.ecommerce.messagesource.model.Recommendation;
import com.ecommerce.messagesource.model.UserBehavior;
import com.ecommerce.messagesource.service.ProductService;
import com.ecommerce.messagesource.service.UserBehaviorService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 消息源软件主应用程序
 */
public class MessageSourceApplication {
    
    private static final Logger logger = LoggerFactory.getLogger(MessageSourceApplication.class);
    
    private static final String KAFKA_BOOTSTRAP_SERVERS = "localhost:9092,localhost:9093,localhost:9094";
    private static final String CONSUMER_GROUP_ID = "message-source-group";
    
    private KafkaProducerService kafkaProducer;
    private KafkaConsumerService kafkaConsumer;
    private ProductService productService;
    private UserBehaviorService userBehaviorService;
    private ScheduledExecutorService scheduler;
    private BufferedReader reader;
    
    public static void main(String[] args) {
        MessageSourceApplication app = new MessageSourceApplication();
        app.start();
    }
    
    public void start() {
        logger.info("Starting Message Source Application...");
        
        try {
            // 初始化组件
            initializeComponents();
            
            // 启动推荐结果消费者
            startRecommendationConsumer();
            
            // 启动自动行为模拟器（可选）
            startAutoSimulator();
            
            // 启动交互式命令行界面
            startInteractiveMode();
            
        } catch (Exception e) {
            logger.error("Failed to start application", e);
        } finally {
            shutdown();
        }
    }
    
    private void initializeComponents() {
        logger.info("Initializing components...");
        
        // 初始化Kafka生产者
        kafkaProducer = new KafkaProducerService(KAFKA_BOOTSTRAP_SERVERS);
        
        // 初始化Kafka消费者
        kafkaConsumer = new KafkaConsumerService(KAFKA_BOOTSTRAP_SERVERS, CONSUMER_GROUP_ID);
        
        // 初始化服务
        productService = new ProductService(kafkaProducer);
        userBehaviorService = new UserBehaviorService(kafkaProducer, productService);
        
        // 初始化调度器
        scheduler = Executors.newScheduledThreadPool(2);
        
        // 初始化输入读取器
        reader = new BufferedReader(new InputStreamReader(System.in));
        
        logger.info("Components initialized successfully");
    }
    
    private void startRecommendationConsumer() {
        logger.info("Starting recommendation consumer...");
        
        kafkaConsumer.startConsumingRecommendations("recommendations", this::handleRecommendation);
        
        logger.info("Recommendation consumer started");
    }
    
    private void handleRecommendation(Recommendation recommendation) {
        logger.info("=== 收到推荐结果 ===");
        logger.info("用户ID: {}", recommendation.getUserId());
        logger.info("推荐算法: {}", recommendation.getAlgorithm());
        logger.info("置信度: {}", recommendation.getConfidence());
        logger.info("推荐时间: {}", recommendation.getTimestamp());
        
        if (recommendation.getRecommendedProducts() != null) {
            logger.info("推荐商品列表:");
            for (int i = 0; i < recommendation.getRecommendedProducts().size(); i++) {
                Recommendation.RecommendedProduct recProduct = recommendation.getRecommendedProducts().get(i);
                Product product = productService.getProduct(recProduct.getProductId());
                
                logger.info("  {}. 商品ID: {} | 评分: {:.2f} | 原因: {}", 
                           i + 1, 
                           recProduct.getProductId(), 
                           recProduct.getScore(),
                           recProduct.getReason());
                
                if (product != null) {
                    logger.info("     商品名称: {} | 分类: {} | 价格: ¥{}", 
                               product.getProductName(), 
                               product.getCategory(), 
                               product.getPrice());
                }
            }
        }
        logger.info("========================");
    }
    
    private void startAutoSimulator() {
        logger.info("Starting auto behavior simulator...");
        
        // 每30秒自动生成一些用户行为
        scheduler.scheduleAtFixedRate(() -> {
            try {
                userBehaviorService.simulateRandomBehavior();
            } catch (Exception e) {
                logger.error("Error in auto simulator", e);
            }
        }, 10, 30, TimeUnit.SECONDS);
        
        logger.info("Auto behavior simulator started");
    }
    
    private void startInteractiveMode() {
        logger.info("Starting interactive mode...");
        printMenu();
        
        try {
            String input;
            while ((input = reader.readLine()) != null) {
                handleUserInput(input.trim());
            }
        } catch (IOException e) {
            logger.error("Error reading user input", e);
        }
    }
    
    private void printMenu() {
        System.out.println("\n=== 电商推荐系统消息源软件 ===");
        System.out.println("1. 创建新商品");
        System.out.println("2. 创建随机商品");
        System.out.println("3. 模拟用户购买");
        System.out.println("4. 模拟随机用户行为");
        System.out.println("5. 批量模拟用户行为");
        System.out.println("6. 查看所有商品");
        System.out.println("7. 查看系统状态");
        System.out.println("8. 帮助");
        System.out.println("9. 退出");
        System.out.print("请选择操作 (1-9): ");
    }
    
    private void handleUserInput(String input) {
        try {
            switch (input) {
                case "1":
                    createNewProduct();
                    break;
                case "2":
                    createRandomProduct();
                    break;
                case "3":
                    simulateUserPurchase();
                    break;
                case "4":
                    simulateRandomBehavior();
                    break;
                case "5":
                    simulateBatchBehaviors();
                    break;
                case "6":
                    showAllProducts();
                    break;
                case "7":
                    showSystemStatus();
                    break;
                case "8":
                    printMenu();
                    break;
                case "9":
                    System.out.println("正在退出系统...");
                    return;
                default:
                    System.out.println("无效的选择，请重新输入");
                    break;
            }
        } catch (Exception e) {
            logger.error("Error handling user input: {}", input, e);
            System.out.println("操作失败: " + e.getMessage());
        }
        
        System.out.print("\n请选择操作 (1-9): ");
    }
    
    private void createNewProduct() throws IOException {
        System.out.print("请输入商品名称: ");
        String name = reader.readLine();
        
        System.out.print("请输入商品分类: ");
        String category = reader.readLine();
        
        System.out.print("请输入商品价格: ");
        Double price = Double.parseDouble(reader.readLine());
        
        System.out.print("请输入商品描述: ");
        String description = reader.readLine();
        
        System.out.print("请输入商品品牌: ");
        String brand = reader.readLine();
        
        System.out.print("请输入库存数量: ");
        Integer stock = Integer.parseInt(reader.readLine());
        
        Product product = productService.createProduct(name, category, price, description, brand, stock);
        System.out.println("商品创建成功: " + product);
    }
    
    private void createRandomProduct() {
        Product product = productService.createRandomProduct();
        System.out.println("随机商品创建成功: " + product);
    }
    
    private void simulateUserPurchase() throws IOException {
        System.out.print("请输入用户ID: ");
        String userId = reader.readLine();
        
        System.out.print("请输入商品ID: ");
        String productId = reader.readLine();
        
        UserBehavior behavior = userBehaviorService.simulatePurchase(userId, productId);
        if (behavior != null) {
            System.out.println("购买行为模拟成功: " + behavior);
        } else {
            System.out.println("购买行为模拟失败");
        }
    }
    
    private void simulateRandomBehavior() {
        UserBehavior behavior = userBehaviorService.simulateRandomBehavior();
        System.out.println("随机用户行为模拟成功: " + behavior);
    }
    
    private void simulateBatchBehaviors() throws IOException {
        System.out.print("请输入要模拟的行为数量: ");
        int count = Integer.parseInt(reader.readLine());
        
        System.out.println("开始批量模拟用户行为...");
        userBehaviorService.simulateBatchBehaviors(count);
        System.out.println("批量模拟完成");
    }
    
    private void showAllProducts() {
        List<Product> products = productService.getAllProducts();
        System.out.println("\n=== 所有商品列表 ===");
        for (Product product : products) {
            System.out.printf("ID: %s | 名称: %s | 分类: %s | 价格: ¥%.2f | 库存: %d%n",
                             product.getProductId(),
                             product.getProductName(),
                             product.getCategory(),
                             product.getPrice(),
                             product.getStock());
        }
        System.out.println("总计: " + products.size() + " 个商品");
    }
    
    private void showSystemStatus() {
        System.out.println("\n=== 系统状态 ===");
        System.out.println("商品总数: " + productService.getProductCount());
        System.out.println("Kafka消费者状态: " + (kafkaConsumer.isRunning() ? "运行中" : "已停止"));
        System.out.println("自动模拟器状态: " + (scheduler.isShutdown() ? "已停止" : "运行中"));
    }
    
    private void shutdown() {
        logger.info("Shutting down application...");
        
        if (kafkaConsumer != null) {
            kafkaConsumer.stop();
        }
        
        if (kafkaProducer != null) {
            kafkaProducer.close();
        }
        
        if (scheduler != null) {
            scheduler.shutdown();
        }
        
        try {
            if (reader != null) {
                reader.close();
            }
        } catch (IOException e) {
            logger.error("Error closing reader", e);
        }
        
        logger.info("Application shutdown completed");
    }
}
