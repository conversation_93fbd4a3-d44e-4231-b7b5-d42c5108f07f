#!/bin/bash

# 使用国内镜像启动Docker服务

set -e

echo "=== 使用国内镜像启动分布式电商推荐系统 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置Docker镜像加速器
configure_docker_mirrors() {
    log_info "配置Docker镜像加速器..."
    
    # 创建Docker配置目录
    sudo mkdir -p /etc/docker
    
    # 配置镜像加速器
    sudo tee /etc/docker/daemon.json <<-'EOF'
{
  "registry-mirrors": [
    "https://registry.cn-hangzhou.aliyuncs.com",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com",
    "https://docker.mirrors.ustc.edu.cn",
    "https://dockerproxy.com"
  ],
  "max-concurrent-downloads": 10,
  "max-concurrent-uploads": 5,
  "log-driver": "json-file",
  "log-level": "warn",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  },
  "storage-driver": "overlay2"
}
EOF
    
    # 重启Docker服务
    log_info "重启Docker服务..."
    sudo systemctl daemon-reload
    sudo systemctl restart docker
    
    # 验证配置
    log_info "验证Docker配置..."
    docker info | grep -A 10 "Registry Mirrors" || log_warn "无法显示镜像配置"
    
    log_info "Docker镜像加速器配置完成"
}

# 尝试启动服务
try_start_services() {
    local compose_file=$1
    local description=$2
    
    log_info "尝试使用 $description..."
    
    # 停止现有服务
    docker-compose -f "$compose_file" down 2>/dev/null || true
    
    # 启动服务
    if timeout 300 docker-compose -f "$compose_file" up -d; then
        log_info "$description 启动成功！"
        return 0
    else
        log_warn "$description 启动失败"
        docker-compose -f "$compose_file" down 2>/dev/null || true
        return 1
    fi
}

# 等待服务启动
wait_for_services() {
    log_info "等待服务启动..."
    
    # 等待Kafka启动
    log_info "等待Kafka启动..."
    for i in {1..30}; do
        if docker exec kafka1 kafka-topics --list --bootstrap-server localhost:9092 2>/dev/null; then
            log_info "Kafka已启动"
            break
        fi
        sleep 2
        echo -n "."
    done
    echo ""
    
    # 等待Flink启动
    log_info "等待Flink启动..."
    for i in {1..30}; do
        if curl -s http://localhost:8081 > /dev/null 2>&1; then
            log_info "Flink已启动"
            break
        fi
        sleep 2
        echo -n "."
    done
    echo ""
}

# 创建Kafka主题
create_kafka_topics() {
    log_info "创建Kafka主题..."
    
    # 等待Kafka完全启动
    sleep 10
    
    # 创建主题
    docker exec kafka1 kafka-topics --create \
        --topic user-behavior \
        --bootstrap-server localhost:9092 \
        --partitions 3 \
        --replication-factor 1 \
        --if-not-exists || log_warn "创建user-behavior主题失败"
    
    docker exec kafka1 kafka-topics --create \
        --topic product-events \
        --bootstrap-server localhost:9092 \
        --partitions 3 \
        --replication-factor 1 \
        --if-not-exists || log_warn "创建product-events主题失败"
    
    docker exec kafka1 kafka-topics --create \
        --topic recommendations \
        --bootstrap-server localhost:9092 \
        --partitions 3 \
        --replication-factor 1 \
        --if-not-exists || log_warn "创建recommendations主题失败"
    
    # 列出所有主题
    log_info "当前Kafka主题:"
    docker exec kafka1 kafka-topics --list --bootstrap-server localhost:9092
}

# 构建项目
build_projects() {
    log_info "构建Java项目..."
    
    # 构建消息源软件
    if [ -d "message-source" ]; then
        log_info "构建消息源软件..."
        cd message-source
        mvn clean package -DskipTests -q
        cd ..
    fi
    
    # 构建Flink推荐引擎
    if [ -d "flink-recommendation" ]; then
        log_info "构建Flink推荐引擎..."
        cd flink-recommendation
        mvn clean package -DskipTests -q
        cd ..
    fi
    
    log_info "项目构建完成"
}

# 显示服务状态
show_status() {
    log_info "=== 服务状态 ==="
    
    echo "Docker容器状态:"
    docker-compose ps
    
    echo ""
    echo "🌐 Web界面:"
    echo "  Flink Dashboard: http://localhost:8081"
    
    echo ""
    echo "🔌 端口:"
    echo "  Kafka: 9092"
    echo "  ZooKeeper: 2181"
    echo "  Redis: 6379"
    echo "  Flink Web UI: 8081"
    
    echo ""
    echo "📊 Kafka主题:"
    docker exec kafka1 kafka-topics --list --bootstrap-server localhost:9092 2>/dev/null || echo "  无法获取主题列表"
}

# 主函数
main() {
    log_info "开始启动Docker服务..."
    
    # 配置Docker镜像加速器
    configure_docker_mirrors
    
    # 尝试不同的compose文件
    if try_start_services "docker-compose-lite.yml" "轻量级镜像方案"; then
        success=true
    elif try_start_services "docker-compose-cn.yml" "国内镜像方案"; then
        success=true
    elif try_start_services "docker-compose.yml" "原始镜像方案"; then
        success=true
    else
        log_error "所有启动方案都失败了"
        log_error "请检查网络连接或尝试手动下载镜像"
        exit 1
    fi
    
    if [ "$success" = true ]; then
        wait_for_services
        create_kafka_topics
        build_projects
        show_status
        
        log_info "=== 系统启动完成 ==="
        log_info ""
        log_info "🚀 下一步:"
        log_info "1. 启动消息源软件: ./scripts/start-message-source.sh"
        log_info "2. 提交Flink作业: ./scripts/submit-flink-job.sh"
        log_info "3. 启动后端API: cd backend && mvn spring-boot:run"
        log_info "4. 启动前端界面: cd frontend && npm install && npm start"
    fi
}

# 执行主函数
main "$@"
