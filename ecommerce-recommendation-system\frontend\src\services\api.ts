import axios from 'axios';

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token等
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    console.error('API Error:', error);
    
    if (error.response) {
      // 服务器返回错误状态码
      const { status, data } = error.response;
      
      switch (status) {
        case 401:
          // 未授权，可以跳转到登录页
          break;
        case 403:
          // 禁止访问
          break;
        case 404:
          // 资源不存在
          break;
        case 500:
          // 服务器内部错误
          break;
        default:
          break;
      }
      
      return Promise.reject(data || error.response);
    } else if (error.request) {
      // 网络错误
      return Promise.reject({ message: '网络连接失败，请检查网络设置' });
    } else {
      // 其他错误
      return Promise.reject({ message: error.message });
    }
  }
);

// API接口定义
export const getSystemStatus = () => {
  return api.get('/system/status');
};

export const getMetrics = () => {
  return api.get('/system/metrics');
};

export const getKafkaTopics = () => {
  return api.get('/system/kafka/topics');
};

export const createKafkaTopic = (topicName: string, partitions: number, replicationFactor: number) => {
  return api.post('/system/kafka/topics/create', null, {
    params: {
      topicName,
      partitions,
      replicationFactor,
    },
  });
};

export const healthCheck = () => {
  return api.get('/system/health');
};

export default api;
