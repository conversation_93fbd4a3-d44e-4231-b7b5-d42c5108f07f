#!/bin/bash

# 提交Flink作业脚本

set -e

echo "=== 提交Flink推荐作业 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Flink环境
check_flink() {
    log_info "检查Flink环境..."
    
    # 检查Flink JobManager是否运行
    if ! docker ps | grep -q jobmanager; then
        log_error "Flink JobManager未运行，请先启动Flink集群"
        log_info "运行: docker-compose up -d"
        exit 1
    fi
    
    # 检查Flink Web UI是否可访问
    if curl -s http://localhost:8081 > /dev/null; then
        log_info "Flink Web UI可访问: http://localhost:8081"
    else
        log_warn "Flink Web UI暂时不可访问，但JobManager正在运行"
    fi
}

# 检查JAR文件
check_jar() {
    JAR_FILE="flink-recommendation/target/flink-recommendation-1.0.0.jar"
    
    if [ ! -f "$JAR_FILE" ]; then
        log_warn "未找到JAR文件，开始构建..."
        cd flink-recommendation
        mvn clean package -DskipTests
        cd ..
        
        if [ ! -f "$JAR_FILE" ]; then
            log_error "构建失败，无法找到JAR文件"
            exit 1
        fi
    fi
    
    log_info "找到JAR文件: $JAR_FILE"
}

# 检查Kafka主题
check_kafka_topics() {
    log_info "检查Kafka主题..."
    
    REQUIRED_TOPICS=("user-behavior" "product-events" "recommendations")
    
    for topic in "${REQUIRED_TOPICS[@]}"; do
        if docker exec kafka1 kafka-topics --list --bootstrap-server localhost:9092 | grep -q "^$topic$"; then
            log_info "主题存在: $topic"
        else
            log_warn "主题不存在: $topic，正在创建..."
            docker exec kafka1 kafka-topics --create \
                --topic "$topic" \
                --bootstrap-server localhost:9092 \
                --partitions 3 \
                --replication-factor 3 \
                --if-not-exists
            log_info "主题创建完成: $topic"
        fi
    done
}

# 提交Flink作业
submit_flink_job() {
    log_info "提交Flink作业..."
    
    JAR_FILE="flink-recommendation/target/flink-recommendation-1.0.0.jar"
    JOB_NAME="E-commerce Recommendation System"
    
    # 检查是否已有同名作业运行
    RUNNING_JOBS=$(docker exec jobmanager flink list 2>/dev/null | grep "RUNNING" | grep "$JOB_NAME" || true)
    
    if [ ! -z "$RUNNING_JOBS" ]; then
        log_warn "发现正在运行的同名作业:"
        echo "$RUNNING_JOBS"
        read -p "是否要取消现有作业并重新提交? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            # 获取作业ID并取消
            JOB_ID=$(echo "$RUNNING_JOBS" | awk '{print $4}')
            log_info "取消作业: $JOB_ID"
            docker exec jobmanager flink cancel "$JOB_ID"
            sleep 5
        else
            log_info "保持现有作业运行"
            exit 0
        fi
    fi
    
    # 提交新作业
    log_info "提交新的Flink作业..."
    
    # 复制JAR文件到JobManager容器
    docker cp "$JAR_FILE" jobmanager:/opt/flink/
    
    # 提交作业
    JOB_SUBMIT_RESULT=$(docker exec jobmanager flink run \
        --class com.ecommerce.flink.RecommendationJob \
        --parallelism 3 \
        /opt/flink/flink-recommendation-1.0.0.jar)
    
    echo "$JOB_SUBMIT_RESULT"
    
    # 提取作业ID
    JOB_ID=$(echo "$JOB_SUBMIT_RESULT" | grep -o "Job has been submitted with JobID [a-f0-9]*" | awk '{print $NF}')
    
    if [ ! -z "$JOB_ID" ]; then
        log_info "作业提交成功！"
        log_info "作业ID: $JOB_ID"
        log_info "Flink Web UI: http://localhost:8081"
        
        # 等待作业启动
        log_info "等待作业启动..."
        sleep 10
        
        # 检查作业状态
        JOB_STATUS=$(docker exec jobmanager flink list | grep "$JOB_ID" | awk '{print $2}' || echo "UNKNOWN")
        log_info "作业状态: $JOB_STATUS"
        
        if [ "$JOB_STATUS" = "RUNNING" ]; then
            log_info "作业运行正常！"
        else
            log_warn "作业状态异常，请检查Flink Web UI"
        fi
    else
        log_error "作业提交失败"
        exit 1
    fi
}

# 显示作业状态
show_job_status() {
    log_info "Flink作业状态:"
    docker exec jobmanager flink list 2>/dev/null || log_error "无法获取作业状态"
}

# 显示作业日志
show_job_logs() {
    log_info "最近的JobManager日志:"
    docker logs --tail 50 jobmanager
    
    echo ""
    log_info "最近的TaskManager日志:"
    docker logs --tail 50 taskmanager
}

# 主函数
main() {
    case "${1:-submit}" in
        submit)
            check_flink
            check_jar
            check_kafka_topics
            submit_flink_job
            ;;
        status)
            show_job_status
            ;;
        logs)
            show_job_logs
            ;;
        *)
            echo "用法: $0 {submit|status|logs}"
            echo "  submit - 提交Flink作业"
            echo "  status - 显示作业状态"
            echo "  logs   - 显示作业日志"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
