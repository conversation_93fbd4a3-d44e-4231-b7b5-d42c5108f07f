.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 自定义样式 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.status-card {
  text-align: center;
  padding: 24px;
}

.status-card .ant-statistic-title {
  margin-bottom: 8px;
  font-size: 14px;
  color: #8c8c8c;
}

.status-card .ant-statistic-content {
  font-size: 28px;
  font-weight: 600;
}

.chart-container {
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.service-status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.service-status-item {
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
  text-align: center;
}

.service-status-item .service-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.service-status-item .service-status {
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
}

.service-status.up {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.service-status.down {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.service-status.unknown {
  background: #fffbe6;
  color: #faad14;
  border: 1px solid #ffe58f;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.metric-item:last-child {
  border-bottom: none;
}

.metric-label {
  font-weight: 500;
  color: #595959;
}

.metric-value {
  font-weight: 600;
  color: #262626;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.loading-text {
  margin-top: 16px;
  color: #8c8c8c;
  font-size: 14px;
}

.error-container {
  text-align: center;
  padding: 40px;
}

.error-icon {
  font-size: 48px;
  color: #ff4d4f;
  margin-bottom: 16px;
}

.error-title {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8px;
}

.error-description {
  color: #8c8c8c;
  margin-bottom: 24px;
}

.topic-type-tag {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-buttons .ant-btn {
  padding: 4px 8px;
  height: auto;
  font-size: 12px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .service-status-grid {
    grid-template-columns: 1fr;
  }
  
  .ant-col {
    margin-bottom: 16px;
  }
  
  .chart-container {
    overflow-x: auto;
  }
}
