#!/bin/bash

# 启动消息源软件脚本

set -e

echo "=== 启动消息源软件 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Java环境
check_java() {
    if ! command -v java &> /dev/null; then
        log_error "未找到Java，请先安装Java"
        exit 1
    fi
    
    log_info "Java版本: $(java -version 2>&1 | head -n 1)"
}

# 检查JAR文件
check_jar() {
    JAR_FILE="message-source/target/message-source-1.0.0.jar"
    
    if [ ! -f "$JAR_FILE" ]; then
        log_warn "未找到JAR文件，开始构建..."
        cd message-source
        mvn clean package -DskipTests
        cd ..
        
        if [ ! -f "$JAR_FILE" ]; then
            log_error "构建失败，无法找到JAR文件"
            exit 1
        fi
    fi
    
    log_info "找到JAR文件: $JAR_FILE"
}

# 检查Kafka连接
check_kafka() {
    log_info "检查Kafka连接..."
    
    # 检查Kafka是否运行
    if ! docker ps | grep -q kafka1; then
        log_error "Kafka未运行，请先启动Kafka集群"
        log_info "运行: docker-compose up -d"
        exit 1
    fi
    
    # 测试Kafka连接
    if docker exec kafka1 kafka-topics --list --bootstrap-server localhost:9092 &> /dev/null; then
        log_info "Kafka连接正常"
    else
        log_error "无法连接到Kafka"
        exit 1
    fi
}

# 创建日志目录
create_log_dir() {
    mkdir -p logs
    log_info "日志目录已创建: logs/"
}

# 启动消息源软件
start_message_source() {
    log_info "启动消息源软件..."
    
    JAR_FILE="message-source/target/message-source-1.0.0.jar"
    LOG_FILE="logs/message-source.log"
    PID_FILE="logs/message-source.pid"
    
    # 检查是否已经运行
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p $PID > /dev/null 2>&1; then
            log_warn "消息源软件已经在运行 (PID: $PID)"
            log_info "如需重启，请先运行: ./scripts/stop-message-source.sh"
            exit 1
        else
            log_warn "发现过期的PID文件，删除中..."
            rm -f "$PID_FILE"
        fi
    fi
    
    # 设置JVM参数
    JVM_OPTS="-Xms512m -Xmx1g -XX:+UseG1GC -XX:+PrintGCDetails -XX:+PrintGCTimeStamps"
    
    # 启动应用程序
    nohup java $JVM_OPTS -jar "$JAR_FILE" > "$LOG_FILE" 2>&1 &
    
    # 保存PID
    echo $! > "$PID_FILE"
    
    log_info "消息源软件已启动"
    log_info "PID: $(cat $PID_FILE)"
    log_info "日志文件: $LOG_FILE"
    
    # 等待启动
    log_info "等待应用程序启动..."
    sleep 5
    
    # 检查是否启动成功
    PID=$(cat "$PID_FILE")
    if ps -p $PID > /dev/null 2>&1; then
        log_info "消息源软件启动成功！"
        log_info ""
        log_info "使用说明:"
        log_info "1. 应用程序提供交互式命令行界面"
        log_info "2. 可以创建商品、模拟用户行为等"
        log_info "3. 查看日志: tail -f $LOG_FILE"
        log_info "4. 停止应用: ./scripts/stop-message-source.sh"
        log_info ""
        log_info "如果需要查看实时日志，请运行:"
        log_info "  tail -f $LOG_FILE"
    else
        log_error "消息源软件启动失败"
        log_error "请查看日志文件: $LOG_FILE"
        exit 1
    fi
}

# 显示状态
show_status() {
    PID_FILE="logs/message-source.pid"
    
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p $PID > /dev/null 2>&1; then
            log_info "消息源软件正在运行 (PID: $PID)"
            
            # 显示内存使用情况
            MEMORY_USAGE=$(ps -p $PID -o rss= | awk '{print $1/1024 " MB"}')
            log_info "内存使用: $MEMORY_USAGE"
            
            # 显示运行时间
            START_TIME=$(ps -p $PID -o lstart= | sed 's/^ *//')
            log_info "启动时间: $START_TIME"
        else
            log_warn "消息源软件未运行"
        fi
    else
        log_warn "消息源软件未运行"
    fi
}

# 主函数
main() {
    case "${1:-start}" in
        start)
            check_java
            check_jar
            check_kafka
            create_log_dir
            start_message_source
            ;;
        status)
            show_status
            ;;
        *)
            echo "用法: $0 {start|status}"
            echo "  start  - 启动消息源软件"
            echo "  status - 显示运行状态"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
