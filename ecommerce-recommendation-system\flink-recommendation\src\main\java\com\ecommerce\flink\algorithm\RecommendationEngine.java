package com.ecommerce.flink.algorithm;

import com.ecommerce.flink.model.Product;
import com.ecommerce.flink.model.Recommendation;
import com.ecommerce.flink.model.UserBehavior;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 推荐算法引擎
 */
public class RecommendationEngine {
    
    private static final Logger logger = LoggerFactory.getLogger(RecommendationEngine.class);
    
    // 用户行为历史存储 (userId -> List<UserBehavior>)
    private final Map<String, List<UserBehavior>> userBehaviorHistory = new HashMap<>();
    
    // 商品信息存储 (productId -> Product)
    private final Map<String, Product> productCatalog = new HashMap<>();
    
    // 用户-商品评分矩阵 (userId -> (productId -> score))
    private final Map<String, Map<String, Double>> userProductMatrix = new HashMap<>();
    
    // 商品相似度矩阵 (productId -> (productId -> similarity))
    private final Map<String, Map<String, Double>> productSimilarityMatrix = new HashMap<>();
    
    private static final int MAX_RECOMMENDATIONS = 5;
    private static final int BEHAVIOR_HISTORY_LIMIT = 100;
    private static final double TIME_DECAY_FACTOR = 0.1; // 时间衰减因子
    
    /**
     * 更新商品信息
     */
    public void updateProduct(Product product) {
        productCatalog.put(product.getProductId(), product);
        logger.debug("Updated product: {}", product.getProductId());
    }
    
    /**
     * 处理用户行为并生成推荐
     */
    public Recommendation processUserBehavior(UserBehavior behavior) {
        // 更新用户行为历史
        updateUserBehaviorHistory(behavior);
        
        // 更新用户-商品评分矩阵
        updateUserProductMatrix(behavior);
        
        // 更新商品相似度矩阵
        updateProductSimilarityMatrix();
        
        // 生成推荐
        return generateRecommendation(behavior.getUserId());
    }
    
    /**
     * 更新用户行为历史
     */
    private void updateUserBehaviorHistory(UserBehavior behavior) {
        userBehaviorHistory.computeIfAbsent(behavior.getUserId(), k -> new ArrayList<>())
                          .add(behavior);
        
        // 限制历史记录数量
        List<UserBehavior> history = userBehaviorHistory.get(behavior.getUserId());
        if (history.size() > BEHAVIOR_HISTORY_LIMIT) {
            history.remove(0);
        }
        
        logger.debug("Updated behavior history for user: {}, total behaviors: {}", 
                    behavior.getUserId(), history.size());
    }
    
    /**
     * 更新用户-商品评分矩阵
     */
    private void updateUserProductMatrix(UserBehavior behavior) {
        String userId = behavior.getUserId();
        String productId = behavior.getProductId();
        
        Map<String, Double> userRatings = userProductMatrix.computeIfAbsent(userId, k -> new HashMap<>());
        
        // 计算评分：行为权重 * 时间衰减
        double timeDecay = calculateTimeDecay(behavior.getTimestamp());
        double behaviorWeight = behavior.getBehaviorType().getWeight();
        double score = behaviorWeight * timeDecay;
        
        // 累加评分
        userRatings.merge(productId, score, Double::sum);
        
        logger.debug("Updated rating for user: {}, product: {}, score: {}", 
                    userId, productId, userRatings.get(productId));
    }
    
    /**
     * 计算时间衰减因子
     */
    private double calculateTimeDecay(LocalDateTime timestamp) {
        long hoursAgo = ChronoUnit.HOURS.between(timestamp, LocalDateTime.now());
        return Math.exp(-TIME_DECAY_FACTOR * hoursAgo);
    }
    
    /**
     * 更新商品相似度矩阵
     */
    private void updateProductSimilarityMatrix() {
        Set<String> allProducts = productCatalog.keySet();
        
        for (String product1 : allProducts) {
            Map<String, Double> similarities = productSimilarityMatrix.computeIfAbsent(product1, k -> new HashMap<>());
            
            for (String product2 : allProducts) {
                if (!product1.equals(product2)) {
                    double similarity = calculateProductSimilarity(product1, product2);
                    similarities.put(product2, similarity);
                }
            }
        }
    }
    
    /**
     * 计算商品相似度
     */
    private double calculateProductSimilarity(String productId1, String productId2) {
        Product product1 = productCatalog.get(productId1);
        Product product2 = productCatalog.get(productId2);
        
        if (product1 == null || product2 == null) {
            return 0.0;
        }
        
        double similarity = 0.0;
        
        // 基于分类的相似度
        if (Objects.equals(product1.getCategory(), product2.getCategory())) {
            similarity += 0.4;
        }
        
        // 基于品牌的相似度
        if (Objects.equals(product1.getBrand(), product2.getBrand())) {
            similarity += 0.3;
        }
        
        // 基于价格的相似度
        if (product1.getPrice() != null && product2.getPrice() != null) {
            double priceDiff = Math.abs(product1.getPrice() - product2.getPrice());
            double maxPrice = Math.max(product1.getPrice(), product2.getPrice());
            double priceSimiliarity = 1.0 - (priceDiff / maxPrice);
            similarity += 0.3 * priceSimiliarity;
        }
        
        return Math.min(similarity, 1.0);
    }
    
    /**
     * 生成推荐
     */
    private Recommendation generateRecommendation(String userId) {
        List<Recommendation.RecommendedProduct> recommendations = new ArrayList<>();
        
        // 获取用户评分
        Map<String, Double> userRatings = userProductMatrix.get(userId);
        if (userRatings == null || userRatings.isEmpty()) {
            // 新用户，返回热门商品推荐
            recommendations = generatePopularProductRecommendations();
        } else {
            // 基于协同过滤的推荐
            recommendations = generateCollaborativeFilteringRecommendations(userId, userRatings);
        }
        
        // 创建推荐结果
        Recommendation recommendation = new Recommendation();
        recommendation.setUserId(userId);
        recommendation.setRecommendedProducts(recommendations);
        recommendation.setAlgorithm("Collaborative Filtering + Content-Based");
        recommendation.setTimestamp(LocalDateTime.now());
        recommendation.setConfidence(calculateConfidence(recommendations));
        
        logger.info("Generated recommendation for user: {}, products: {}", 
                   userId, recommendations.size());
        
        return recommendation;
    }
    
    /**
     * 生成热门商品推荐（新用户）
     */
    private List<Recommendation.RecommendedProduct> generatePopularProductRecommendations() {
        // 计算商品热度
        Map<String, Double> productPopularity = new HashMap<>();
        
        for (Map<String, Double> userRatings : userProductMatrix.values()) {
            for (Map.Entry<String, Double> entry : userRatings.entrySet()) {
                productPopularity.merge(entry.getKey(), entry.getValue(), Double::sum);
            }
        }
        
        // 按热度排序并取前N个
        return productPopularity.entrySet().stream()
                .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
                .limit(MAX_RECOMMENDATIONS)
                .map(entry -> new Recommendation.RecommendedProduct(
                    entry.getKey(), 
                    entry.getValue() / userProductMatrix.size(), // 平均评分
                    "热门商品推荐"
                ))
                .collect(Collectors.toList());
    }
    
    /**
     * 基于协同过滤的推荐
     */
    private List<Recommendation.RecommendedProduct> generateCollaborativeFilteringRecommendations(
            String userId, Map<String, Double> userRatings) {
        
        Map<String, Double> candidateScores = new HashMap<>();
        
        // 基于用户已评分的商品，找到相似商品
        for (String ratedProductId : userRatings.keySet()) {
            Map<String, Double> similarities = productSimilarityMatrix.get(ratedProductId);
            if (similarities != null) {
                for (Map.Entry<String, Double> entry : similarities.entrySet()) {
                    String candidateProductId = entry.getKey();
                    double similarity = entry.getValue();
                    
                    // 排除用户已经评分的商品
                    if (!userRatings.containsKey(candidateProductId)) {
                        double userRating = userRatings.get(ratedProductId);
                        double score = similarity * userRating;
                        candidateScores.merge(candidateProductId, score, Double::sum);
                    }
                }
            }
        }
        
        // 按评分排序并取前N个
        return candidateScores.entrySet().stream()
                .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
                .limit(MAX_RECOMMENDATIONS)
                .map(entry -> {
                    Product product = productCatalog.get(entry.getKey());
                    String reason = product != null ? 
                        "基于您对" + product.getCategory() + "类商品的偏好" : 
                        "基于您的购买历史";
                    
                    return new Recommendation.RecommendedProduct(
                        entry.getKey(),
                        Math.min(entry.getValue(), 5.0), // 限制最高评分为5.0
                        reason
                    );
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 计算推荐置信度
     */
    private double calculateConfidence(List<Recommendation.RecommendedProduct> recommendations) {
        if (recommendations.isEmpty()) {
            return 0.0;
        }
        
        double avgScore = recommendations.stream()
                .mapToDouble(Recommendation.RecommendedProduct::getScore)
                .average()
                .orElse(0.0);
        
        return Math.min(avgScore / 5.0, 1.0); // 归一化到0-1
    }
    
    /**
     * 获取统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalUsers", userBehaviorHistory.size());
        stats.put("totalProducts", productCatalog.size());
        stats.put("totalBehaviors", userBehaviorHistory.values().stream()
                .mapToInt(List::size).sum());
        return stats;
    }
}
